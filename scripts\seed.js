const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'raspa_green',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

async function seedDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🌱 Iniciando seed do banco de dados...');
    
    // Limpar dados existentes (exceto usuários admin)
    await client.query('DELETE FROM plays');
    await client.query('DELETE FROM pix_transactions');
    await client.query('DELETE FROM financial_movements');
    await client.query("DELETE FROM users WHERE email != '<EMAIL>'");
    
    console.log('🧹 Dados antigos removidos.');

    // Inserir usuários de teste adicionais
    console.log('👥 Criando usuários de teste...');
    
    const testUsers = [
      {
        email: '<EMAIL>',
        name: '<PERSON> <PERSON>',
        phone: '(11) 99999-1111',
        cpf: '111.111.111-11',
        balance: 100.00
      },
      {
        email: '<EMAIL>',
        name: 'Maria <PERSON>',
        phone: '(11) 99999-2222',
        cpf: '222.222.222-22',
        balance: 250.00
      },
      {
        email: '<EMAIL>',
        name: 'Pedro Oliveira',
        phone: '(11) 99999-3333',
        cpf: '333.333.333-33',
        balance: 50.00
      },
      {
        email: '<EMAIL>',
        name: 'Ana Costa',
        phone: '(11) 99999-4444',
        cpf: '444.444.444-44',
        balance: 500.00
      }
    ];

    for (const user of testUsers) {
      await client.query(`
        INSERT INTO users (email, password_hash, name, phone, cpf, balance)
        VALUES ($1, crypt('123456', gen_salt('bf')), $2, $3, $4, $5)
        ON CONFLICT (email) DO NOTHING
      `, [user.email, user.name, user.phone, user.cpf, user.balance]);
    }

    console.log(`✅ ${testUsers.length} usuários de teste criados.`);

    // Criar algumas jogadas de exemplo
    console.log('🎰 Criando jogadas de exemplo...');
    
    // Buscar usuários e jogos
    const usersResult = await client.query("SELECT id FROM users WHERE email != '<EMAIL>' LIMIT 4");
    const gamesResult = await client.query('SELECT id, price FROM games WHERE is_active = true');
    const prizesResult = await client.query('SELECT id, game_id, value, winning_symbols FROM prizes WHERE is_active = true');

    const users = usersResult.rows;
    const games = gamesResult.rows;
    const prizes = prizesResult.rows;

    // Criar jogadas aleatórias
    for (let i = 0; i < 50; i++) {
      const randomUser = users[Math.floor(Math.random() * users.length)];
      const randomGame = games[Math.floor(Math.random() * games.length)];
      const gamePrizes = prizes.filter(p => p.game_id === randomGame.id);
      const randomPrize = gamePrizes[Math.floor(Math.random() * gamePrizes.length)];
      
      // Gerar símbolos aleatórios
      const symbols = ['🍎', '🍊', '🍋', '🍇', '🍓', '🥝', '🍑', '🍌', '💎'];
      const revealedSymbols = [];
      for (let j = 0; j < 9; j++) {
        revealedSymbols.push(symbols[Math.floor(Math.random() * symbols.length)]);
      }

      // Data aleatória nos últimos 30 dias
      const randomDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      const scratchPercentage = Math.random() * 40 + 60; // Entre 60% e 100%

      await client.query(`
        INSERT INTO plays (
          user_id, game_id, prize_id, amount_paid, amount_won, 
          revealed_symbols, scratch_percentage, status, played_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'completed', $8)
      `, [
        randomUser.id, randomGame.id, randomPrize.id, 
        randomGame.price, randomPrize.value, revealedSymbols, 
        scratchPercentage, randomDate
      ]);
    }

    console.log('✅ 50 jogadas de exemplo criadas.');

    // Criar transações PIX de exemplo
    console.log('💳 Criando transações PIX de exemplo...');
    
    for (let i = 0; i < 20; i++) {
      const randomUser = users[Math.floor(Math.random() * users.length)];
      const isDeposit = Math.random() > 0.5;
      const amount = Math.random() * 500 + 10; // Entre R$ 10 e R$ 510
      const status = ['pending', 'completed', 'failed'][Math.floor(Math.random() * 3)];
      const randomDate = new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000);

      await client.query(`
        INSERT INTO pix_transactions (
          user_id, transaction_id, gateway, type, amount, status, 
          pix_key, cpf, created_at
        ) VALUES ($1, $2, 'SIMULATED', $3, $4, $5, $6, $7, $8)
      `, [
        randomUser.id, 
        `TXN_${Date.now()}_${i}`,
        isDeposit ? 'deposit' : 'withdrawal',
        amount,
        status,
        isDeposit ? null : '<EMAIL>',
        '111.111.111-11',
        randomDate
      ]);
    }

    console.log('✅ 20 transações PIX de exemplo criadas.');

    // Criar movimentações financeiras
    console.log('💰 Criando movimentações financeiras...');
    
    for (const user of users) {
      const userBalance = Math.random() * 1000;
      
      // Depósito inicial
      await client.query(`
        INSERT INTO financial_movements (
          user_id, type, amount, balance_before, balance_after, 
          reference_type, description
        ) VALUES ($1, 'deposit', $2, 0, $2, 'initial', 'Depósito inicial')
      `, [user.id, userBalance]);

      // Algumas apostas
      for (let i = 0; i < 5; i++) {
        const betAmount = Math.random() * 50 + 5;
        const winAmount = Math.random() > 0.7 ? Math.random() * 100 + 10 : 0;
        
        await client.query(`
          INSERT INTO financial_movements (
            user_id, type, amount, balance_before, balance_after, 
            reference_type, description
          ) VALUES ($1, 'bet', $2, $3, $4, 'play', 'Aposta em raspadinha')
        `, [user.id, betAmount, userBalance, userBalance - betAmount]);

        if (winAmount > 0) {
          await client.query(`
            INSERT INTO financial_movements (
              user_id, type, amount, balance_before, balance_after, 
              reference_type, description
            ) VALUES ($1, 'win', $2, $3, $4, 'play', 'Prêmio ganho')
          `, [user.id, winAmount, userBalance - betAmount, userBalance - betAmount + winAmount]);
        }
      }
    }

    console.log('✅ Movimentações financeiras criadas.');

    // Atualizar estatísticas
    console.log('📊 Atualizando estatísticas...');
    
    // Atualizar contadores de uso de símbolos
    await client.query(`
      UPDATE scratch_symbols 
      SET usage_count = (
        SELECT COUNT(*) 
        FROM plays p 
        WHERE scratch_symbols.symbol = ANY(p.revealed_symbols)
      )
    `);

    // Atualizar popularidade dos jogos baseado no número de jogadas
    await client.query(`
      UPDATE games 
      SET popularity_score = LEAST(100, (
        SELECT COUNT(*) * 2
        FROM plays p 
        WHERE p.game_id = games.id
      ))
    `);

    console.log('✅ Estatísticas atualizadas.');

    console.log('');
    console.log('🎉 Seed concluído com sucesso!');
    console.log('');
    console.log('📈 Dados criados:');
    console.log(`   - ${testUsers.length} usuários de teste`);
    console.log('   - 50 jogadas de exemplo');
    console.log('   - 20 transações PIX');
    console.log('   - Movimentações financeiras');
    console.log('   - Estatísticas atualizadas');
    console.log('');
    console.log('👥 Usuários de teste (senha: 123456):');
    testUsers.forEach(user => {
      console.log(`   - ${user.email} (${user.name})`);
    });

  } catch (error) {
    console.error('❌ Erro no seed:', error.message);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

async function main() {
  console.log('🎰 RASPA GREEN - Seed do Banco de Dados');
  console.log('=====================================');
  console.log('');
  
  await seedDatabase();
  
  console.log('');
  console.log('🚀 Dados de exemplo inseridos! O sistema está pronto para testes.');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { seedDatabase };
