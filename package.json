{"name": "raspa-green", "version": "1.0.0", "description": "Plataforma de raspadinhas online com PIX", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:client", "build:client": "vite build", "preview": "vite preview", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "axios": "^1.6.2", "socket.io": "^4.7.4", "uuid": "^9.0.1", "joi": "^17.11.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "vite": "^5.0.8", "@vitejs/plugin-react": "^4.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "keywords": ["raspad<PERSON><PERSON>", "pix", "jogos", "online"], "author": "Raspa Green Team", "license": "MIT"}