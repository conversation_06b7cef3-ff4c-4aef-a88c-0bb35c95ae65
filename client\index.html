<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Raspa Green - Raspadinhas Online com PIX</title>
    <meta name="description" content="Jogue raspadinhas online e ganhe prêmios em dinheiro no PIX! Diversão garantida com pagamentos instantâneos." />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://raspagreen.com/" />
    <meta property="og:title" content="Raspa Green - Raspadinhas Online com PIX" />
    <meta property="og:description" content="Jogue raspadinhas online e ganhe prêmios em dinheiro no PIX! Diversão garantida com pagamentos instantâneos." />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://raspagreen.com/" />
    <meta property="twitter:title" content="Raspa Green - Raspadinhas Online com PIX" />
    <meta property="twitter:description" content="Jogue raspadinhas online e ganhe prêmios em dinheiro no PIX! Diversão garantida com pagamentos instantâneos." />
    <meta property="twitter:image" content="/og-image.jpg" />

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      /* Critical CSS for loading state */
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
        min-height: 100vh;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
      }
      
      .loading-logo {
        font-size: 4rem;
        margin-bottom: 1rem;
        animation: pulse 2s ease-in-out infinite;
      }
      
      .loading-text {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 2rem;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading screen shown while React loads -->
      <div class="loading-screen">
        <div class="loading-logo">🎰</div>
        <div class="loading-text">Raspa Green</div>
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
