const express = require('express');
const Joi = require('joi');
const pool = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

const router = express.Router();

// Middleware para verificar se é admin
router.use(authenticateToken);
router.use(requireRole(['admin', 'moderator']));

// Schema de validação para criação de jogos
const createGameSchema = Joi.object({
  name: Joi.string().min(3).max(255).required(),
  description: Joi.string().max(500).optional(),
  price: Joi.number().min(0.01).max(1000).required(),
  maxPrize: Joi.number().min(1).max(1000000).required(),
  imageUrl: Joi.string().uri().optional(),
  symbols: Joi.array().items(Joi.string()).min(3).max(20).required(),
  winningCombinations: Joi.array().items(Joi.object({
    symbols: Joi.array().items(Joi.string()).required(),
    prize: Joi.number().min(0).required()
  })).optional(),
  scratchPercentageRequired: Joi.number().min(50).max(100).default(80),
  popularityScore: Joi.number().min(0).max(100).default(50)
});

// Dashboard - estatísticas gerais
router.get('/dashboard', async (req, res, next) => {
  try {
    // Estatísticas gerais
    const statsResult = await pool.query(`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE is_active = true) as total_users,
        (SELECT COUNT(*) FROM games WHERE is_active = true) as total_games,
        (SELECT COUNT(*) FROM plays WHERE status = 'completed' AND DATE(played_at) = CURRENT_DATE) as today_plays,
        (SELECT COALESCE(SUM(amount_paid), 0) FROM plays WHERE status = 'completed' AND DATE(played_at) = CURRENT_DATE) as today_revenue,
        (SELECT COALESCE(SUM(amount_won), 0) FROM plays WHERE status = 'completed' AND DATE(played_at) = CURRENT_DATE) as today_prizes,
        (SELECT COUNT(*) FROM pix_transactions WHERE status = 'pending') as pending_transactions
    `);

    // Top jogos por receita (últimos 7 dias)
    const topGamesResult = await pool.query(`
      SELECT 
        g.name,
        g.price,
        COUNT(p.id) as total_plays,
        SUM(p.amount_paid) as total_revenue,
        SUM(p.amount_won) as total_prizes
      FROM games g
      LEFT JOIN plays p ON g.id = p.game_id 
        AND p.status = 'completed' 
        AND p.played_at >= CURRENT_DATE - INTERVAL '7 days'
      WHERE g.is_active = true
      GROUP BY g.id, g.name, g.price
      ORDER BY total_revenue DESC NULLS LAST
      LIMIT 5
    `);

    // Usuários mais ativos (últimos 7 dias)
    const topUsersResult = await pool.query(`
      SELECT 
        u.name,
        u.email,
        COUNT(p.id) as total_plays,
        SUM(p.amount_paid) as total_spent,
        SUM(p.amount_won) as total_won
      FROM users u
      LEFT JOIN plays p ON u.id = p.user_id 
        AND p.status = 'completed' 
        AND p.played_at >= CURRENT_DATE - INTERVAL '7 days'
      WHERE u.is_active = true
      GROUP BY u.id, u.name, u.email
      ORDER BY total_plays DESC NULLS LAST
      LIMIT 5
    `);

    res.json({
      stats: statsResult.rows[0],
      topGames: topGamesResult.rows,
      topUsers: topUsersResult.rows
    });
  } catch (error) {
    next(error);
  }
});

// Listar todos os usuários
router.get('/users', async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search;
    const role = req.query.role;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramCount = 1;

    if (search) {
      whereClause += ` AND (name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    if (role) {
      whereClause += ` AND role = $${paramCount}`;
      queryParams.push(role);
      paramCount++;
    }

    const result = await pool.query(`
      SELECT 
        id, email, name, phone, cpf, balance, role, permissions, 
        is_active, email_verified, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...queryParams, limit, offset]);

    const countResult = await pool.query(`
      SELECT COUNT(*) FROM users ${whereClause}
    `, queryParams);

    const totalCount = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      users: result.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    next(error);
  }
});

// Atualizar usuário
router.put('/users/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, phone, role, permissions, isActive, balance } = req.body;

    const updateFields = [];
    const updateValues = [];
    let paramCount = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramCount++}`);
      updateValues.push(name);
    }
    if (phone !== undefined) {
      updateFields.push(`phone = $${paramCount++}`);
      updateValues.push(phone);
    }
    if (role !== undefined) {
      updateFields.push(`role = $${paramCount++}`);
      updateValues.push(role);
    }
    if (permissions !== undefined) {
      updateFields.push(`permissions = $${paramCount++}`);
      updateValues.push(permissions);
    }
    if (isActive !== undefined) {
      updateFields.push(`is_active = $${paramCount++}`);
      updateValues.push(isActive);
    }
    if (balance !== undefined) {
      updateFields.push(`balance = $${paramCount++}`);
      updateValues.push(balance);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'Nenhum campo para atualizar' });
    }

    updateValues.push(id);

    const result = await pool.query(`
      UPDATE users 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING id, email, name, phone, role, permissions, is_active, balance, updated_at
    `, updateValues);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    res.json({
      message: 'Usuário atualizado com sucesso',
      user: result.rows[0]
    });
  } catch (error) {
    next(error);
  }
});

// Listar todos os jogos
router.get('/games', async (req, res, next) => {
  try {
    const result = await pool.query(`
      SELECT 
        g.*,
        COUNT(p.id) as total_plays,
        COALESCE(SUM(p.amount_paid), 0) as total_revenue,
        COALESCE(SUM(p.amount_won), 0) as total_prizes,
        COALESCE(AVG(p.scratch_percentage), 0) as avg_scratch_percentage
      FROM games g
      LEFT JOIN plays p ON g.id = p.game_id AND p.status = 'completed'
      GROUP BY g.id
      ORDER BY g.created_at DESC
    `);

    res.json(result.rows);
  } catch (error) {
    next(error);
  }
});

// Criar novo jogo
router.post('/games', requireRole(['admin']), async (req, res, next) => {
  try {
    const { error, value } = createGameSchema.validate(req.body);
    if (error) throw error;

    const {
      name, description, price, maxPrize, imageUrl, symbols,
      winningCombinations, scratchPercentageRequired, popularityScore
    } = value;

    const result = await pool.query(`
      INSERT INTO games (
        name, description, price, max_prize, image_url, symbols,
        winning_combinations, scratch_percentage_required, popularity_score
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [
      name, description, price, maxPrize, imageUrl, symbols,
      JSON.stringify(winningCombinations || []), scratchPercentageRequired, popularityScore
    ]);

    res.json({
      message: 'Jogo criado com sucesso',
      game: result.rows[0]
    });
  } catch (error) {
    next(error);
  }
});

// Atualizar jogo
router.put('/games/:id', requireRole(['admin']), async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      name, description, price, maxPrize, imageUrl, symbols,
      winningCombinations, scratchPercentageRequired, popularityScore, isActive
    } = req.body;

    const updateFields = [];
    const updateValues = [];
    let paramCount = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramCount++}`);
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramCount++}`);
      updateValues.push(description);
    }
    if (price !== undefined) {
      updateFields.push(`price = $${paramCount++}`);
      updateValues.push(price);
    }
    if (maxPrize !== undefined) {
      updateFields.push(`max_prize = $${paramCount++}`);
      updateValues.push(maxPrize);
    }
    if (imageUrl !== undefined) {
      updateFields.push(`image_url = $${paramCount++}`);
      updateValues.push(imageUrl);
    }
    if (symbols !== undefined) {
      updateFields.push(`symbols = $${paramCount++}`);
      updateValues.push(symbols);
    }
    if (winningCombinations !== undefined) {
      updateFields.push(`winning_combinations = $${paramCount++}`);
      updateValues.push(JSON.stringify(winningCombinations));
    }
    if (scratchPercentageRequired !== undefined) {
      updateFields.push(`scratch_percentage_required = $${paramCount++}`);
      updateValues.push(scratchPercentageRequired);
    }
    if (popularityScore !== undefined) {
      updateFields.push(`popularity_score = $${paramCount++}`);
      updateValues.push(popularityScore);
    }
    if (isActive !== undefined) {
      updateFields.push(`is_active = $${paramCount++}`);
      updateValues.push(isActive);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'Nenhum campo para atualizar' });
    }

    updateValues.push(id);

    const result = await pool.query(`
      UPDATE games 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, updateValues);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Jogo não encontrado' });
    }

    res.json({
      message: 'Jogo atualizado com sucesso',
      game: result.rows[0]
    });
  } catch (error) {
    next(error);
  }
});

// Relatório de transações PIX
router.get('/pix-transactions', async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const status = req.query.status;
    const type = req.query.type;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramCount = 1;

    if (status) {
      whereClause += ` AND pt.status = $${paramCount}`;
      queryParams.push(status);
      paramCount++;
    }

    if (type) {
      whereClause += ` AND pt.type = $${paramCount}`;
      queryParams.push(type);
      paramCount++;
    }

    const result = await pool.query(`
      SELECT 
        pt.*,
        u.name as user_name,
        u.email as user_email
      FROM pix_transactions pt
      JOIN users u ON pt.user_id = u.id
      ${whereClause}
      ORDER BY pt.created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...queryParams, limit, offset]);

    const countResult = await pool.query(`
      SELECT COUNT(*) FROM pix_transactions pt
      JOIN users u ON pt.user_id = u.id
      ${whereClause}
    `, queryParams);

    const totalCount = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      transactions: result.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    next(error);
  }
});

// Relatórios financeiros
router.get('/reports/financial', async (req, res, next) => {
  try {
    const startDate = req.query.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const endDate = req.query.endDate || new Date().toISOString().split('T')[0];

    // Receita por dia
    const dailyRevenueResult = await pool.query(`
      SELECT * FROM daily_revenue 
      WHERE play_date BETWEEN $1 AND $2
      ORDER BY play_date DESC
    `, [startDate, endDate]);

    // Resumo geral
    const summaryResult = await pool.query(`
      SELECT 
        COUNT(*) as total_plays,
        SUM(amount_paid) as total_revenue,
        SUM(amount_won) as total_prizes,
        SUM(amount_paid) - SUM(amount_won) as net_profit,
        AVG(scratch_percentage) as avg_scratch_percentage
      FROM plays 
      WHERE status = 'completed' 
        AND DATE(played_at) BETWEEN $1 AND $2
    `, [startDate, endDate]);

    res.json({
      summary: summaryResult.rows[0],
      dailyRevenue: dailyRevenueResult.rows
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
