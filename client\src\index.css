@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-gradient-gold text-white hover:opacity-90 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply btn bg-white text-primary-700 hover:bg-gray-50 border border-gray-200;
  }
  
  .btn-success {
    @apply btn bg-gradient-green text-white hover:opacity-90 shadow-lg hover:shadow-xl;
  }
  
  .btn-lg {
    @apply h-11 px-8 text-base;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-sm {
    @apply h-9 px-3 text-sm;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-lg border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  .scratch-card {
    @apply relative bg-white rounded-xl shadow-2xl border-4 border-gold-400 overflow-hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }
  
  .scratch-overlay {
    @apply absolute inset-0 cursor-crosshair;
    background: linear-gradient(45deg, #c0c0c0 25%, transparent 25%), 
                linear-gradient(-45deg, #c0c0c0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #c0c0c0 75%), 
                linear-gradient(-45deg, transparent 75%, #c0c0c0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }
  
  .winning-animation {
    @apply animate-pulse-gold;
  }
  
  .balance-display {
    @apply bg-gradient-to-r from-gold-400 to-gold-600 text-white font-bold px-4 py-2 rounded-lg shadow-lg;
  }
  
  .game-card {
    @apply card hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1;
  }
  
  .game-card:hover {
    @apply ring-2 ring-primary-400 ring-opacity-50;
  }
  
  .prize-badge {
    @apply bg-gradient-gold text-white font-bold px-3 py-1 rounded-full text-sm shadow-lg;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-4 border-gray-200 border-t-primary-600;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .bg-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-500;
}

/* Scratch card specific styles */
.scratch-area {
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.scratch-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
  touch-action: none;
}

/* Animation for winning */
@keyframes confetti {
  0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
  100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

.confetti {
  position: fixed;
  width: 10px;
  height: 10px;
  background: #fbbf24;
  animation: confetti 3s linear infinite;
  z-index: 1000;
}

.confetti:nth-child(odd) {
  background: #22c55e;
  animation-delay: 0.5s;
}

.confetti:nth-child(3n) {
  background: #ef4444;
  animation-delay: 1s;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .scratch-card {
    @apply mx-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-lg;
  }
  
  .card-body {
    @apply px-4 py-3;
  }
}
