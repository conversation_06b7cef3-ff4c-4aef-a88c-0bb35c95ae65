const express = require('express');
const pool = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Listar jogos ativos
router.get('/', async (req, res, next) => {
  try {
    const result = await pool.query(`
      SELECT 
        g.*,
        COUNT(p.id) as total_plays,
        AVG(p.scratch_percentage) as avg_scratch_percentage
      FROM games g
      LEFT JOIN plays p ON g.id = p.game_id AND p.status = 'completed'
      WHERE g.is_active = true
      GROUP BY g.id
      ORDER BY g.popularity_score DESC, g.created_at DESC
    `);

    res.json(result.rows);
  } catch (error) {
    next(error);
  }
});

// Buscar jogo por ID
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const gameResult = await pool.query(
      'SELECT * FROM games WHERE id = $1 AND is_active = true',
      [id]
    );

    if (gameResult.rows.length === 0) {
      return res.status(404).json({ error: 'Jogo não encontrado' });
    }

    const prizesResult = await pool.query(
      'SELECT * FROM prizes WHERE game_id = $1 AND is_active = true ORDER BY probability DESC',
      [id]
    );

    const game = gameResult.rows[0];
    game.prizes = prizesResult.rows;

    res.json(game);
  } catch (error) {
    next(error);
  }
});

// Buscar símbolos disponíveis
router.get('/symbols/available', async (req, res, next) => {
  try {
    const result = await pool.query(`
      SELECT * FROM scratch_symbols 
      WHERE is_active = true 
      ORDER BY category, usage_count DESC
    `);

    // Agrupar por categoria
    const symbolsByCategory = result.rows.reduce((acc, symbol) => {
      if (!acc[symbol.category]) {
        acc[symbol.category] = [];
      }
      acc[symbol.category].push(symbol);
      return acc;
    }, {});

    res.json(symbolsByCategory);
  } catch (error) {
    next(error);
  }
});

module.exports = router;