const errorHandler = (err, req, res, next) => {
  console.error('❌ Erro:', err);

  // Erro de validação do Joi
  if (err.isJoi) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: err.details.map(detail => detail.message)
    });
  }

  // Erro do PostgreSQL
  if (err.code) {
    switch (err.code) {
      case '23505': // Unique violation
        return res.status(409).json({
          error: 'Dados já existem',
          message: 'Este email ou CPF já está cadastrado'
        });
      case '23503': // Foreign key violation
        return res.status(400).json({
          error: 'Referência inválida',
          message: 'Dados relacionados não encontrados'
        });
      case '23514': // Check violation
        return res.status(400).json({
          error: 'Dados inválidos',
          message: 'Valores não atendem às restrições'
        });
    }
  }

  // Erro JWT
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      error: 'Token inválido'
    });
  }

  // Erro padrão
  res.status(err.status || 500).json({
    error: err.message || 'Erro interno do servidor',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

module.exports = { errorHandler };