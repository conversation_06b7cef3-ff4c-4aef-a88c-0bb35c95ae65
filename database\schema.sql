-- Criação do banco de dados para Raspa Green
-- PostgreSQL Schema com sistema de símbolos

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Tabela de usuários
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    cpf VARCHAR(14) UNIQUE,
    balance DECIMAL(10,2) DEFAULT 0.00,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user', 'moderator', 'operator', 'viewer')),
    permissions TEXT[] DEFAULT ARRAY['play', 'deposit', 'withdraw'],
    avatar TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de símbolos para raspadinhas
CREATE TABLE scratch_symbols (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(10) NOT NULL,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de jogos/raspadinhas
CREATE TABLE games (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    max_prize DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    symbols TEXT[] DEFAULT ARRAY[]::TEXT[],
    winning_combinations JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT true,
    popularity_score INTEGER DEFAULT 0,
    scratch_percentage_required INTEGER DEFAULT 80,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de prêmios
CREATE TABLE prizes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    type VARCHAR(20) DEFAULT 'money' CHECK (type IN ('money', 'item', 'bonus')),
    probability DECIMAL(5,2) NOT NULL,
    winning_symbols TEXT[] DEFAULT ARRAY[]::TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de jogadas
CREATE TABLE plays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    prize_id UUID REFERENCES prizes(id),
    amount_paid DECIMAL(10,2) NOT NULL,
    amount_won DECIMAL(10,2) DEFAULT 0.00,
    revealed_symbols TEXT[] DEFAULT ARRAY[]::TEXT[],
    scratch_percentage DECIMAL(5,2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled')),
    played_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    device_info JSONB DEFAULT '{}'::jsonb
);

-- Tabela de transações PIX
CREATE TABLE pix_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    gateway VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'withdrawal')),
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    qr_code TEXT,
    pix_key TEXT,
    cpf VARCHAR(14),
    webhook_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Tabela de movimentações financeiras
CREATE TABLE financial_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'win', 'bet', 'bonus', 'refund')),
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de sessões de usuário
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de logs do sistema
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índices para performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_cpf ON users(cpf);
CREATE INDEX idx_scratch_symbols_category ON scratch_symbols(category);
CREATE INDEX idx_scratch_symbols_active ON scratch_symbols(is_active);
CREATE INDEX idx_games_active ON games(is_active);
CREATE INDEX idx_games_price ON games(price);
CREATE INDEX idx_plays_user_id ON plays(user_id);
CREATE INDEX idx_plays_game_id ON plays(game_id);
CREATE INDEX idx_plays_played_at ON plays(played_at);
CREATE INDEX idx_pix_transactions_user_id ON pix_transactions(user_id);
CREATE INDEX idx_pix_transactions_status ON pix_transactions(status);
CREATE INDEX idx_pix_transactions_created_at ON pix_transactions(created_at);
CREATE INDEX idx_financial_movements_user_id ON financial_movements(user_id);
CREATE INDEX idx_financial_movements_created_at ON financial_movements(created_at);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scratch_symbols_updated_at BEFORE UPDATE ON scratch_symbols
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_games_updated_at BEFORE UPDATE ON games
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inserir símbolos padrão
INSERT INTO scratch_symbols (symbol, name, category, usage_count) VALUES
('🍎', 'Maçã', 'Frutas', 45),
('🍊', 'Laranja', 'Frutas', 38),
('🍋', 'Limão', 'Frutas', 32),
('🍇', 'Uva', 'Frutas', 28),
('🍓', 'Morango', 'Frutas', 25),
('🥝', 'Kiwi', 'Frutas', 15),
('🍑', 'Cereja', 'Frutas', 12),
('🍌', 'Banana', 'Frutas', 22),
('💎', 'Diamante', 'Joias', 8),
('👑', 'Coroa', 'Joias', 5),
('🎰', 'Slot', 'Casino', 0),
('🎲', 'Dado', 'Casino', 18),
('🔥', 'Fogo', 'Elementos', 20),
('⚡', 'Raio', 'Elementos', 15),
('💫', 'Estrela Cadente', 'Elementos', 10),
('🌟', 'Estrela', 'Elementos', 12),
('✨', 'Brilho', 'Elementos', 8),
('🎯', 'Alvo', 'Esportes', 6),
('🏆', 'Troféu', 'Esportes', 4),
('🥇', 'Medalha de Ouro', 'Esportes', 3),
('💰', 'Saco de Dinheiro', 'Dinheiro', 2),
('💸', 'Dinheiro Voando', 'Dinheiro', 1),
('💵', 'Nota de Dólar', 'Dinheiro', 1),
('🍀', 'Trevo', 'Sorte', 10);

-- Inserir dados de teste
INSERT INTO users (email, password_hash, name, phone, cpf, balance, role, permissions) VALUES
('<EMAIL>', crypt('123456789', gen_salt('bf')), 'Administrador', '(11) 99999-9999', '123.456.789-00', 1000.00, 'admin', ARRAY['*']),
('<EMAIL>', crypt('123456', gen_salt('bf')), 'Usuário Teste', '(11) 88888-8888', '987.654.321-00', 50.00, 'user', ARRAY['play', 'deposit', 'withdraw']),
('<EMAIL>', crypt('123456', gen_salt('bf')), 'Moderador', '(11) 77777-7777', '456.789.123-00', 200.00, 'moderator', ARRAY['play', 'deposit', 'withdraw', 'moderate']),
('<EMAIL>', crypt('123456', gen_salt('bf')), 'Operador', '(11) 66666-6666', '789.123.456-00', 100.00, 'operator', ARRAY['play', 'deposit', 'withdraw', 'operate']);

-- Inserir jogos de teste com símbolos
INSERT INTO games (name, description, price, max_prize, symbols, winning_combinations, scratch_percentage_required, popularity_score) VALUES
('Raspa da Esperança', 'PRÊMIOS ATÉ R$1.000,00 NO PIX', 1.00, 1000.00, 
 ARRAY['🍎', '🍊', '🍋', '🍇', '🍓'], 
 '[{"symbols": ["🍎", "🍎", "🍎"], "prize": 5}, {"symbols": ["🍊", "🍊", "🍊"], "prize": 10}, {"symbols": ["🍋", "🍋", "🍋"], "prize": 25}]'::jsonb,
 80, 85),

('Raspa da Alegria', 'PRÊMIOS ATÉ R$5.000,00 NO PIX', 2.00, 5000.00,
 ARRAY['🍇', '🍓', '🥝', '🍑', '🍌'],
 '[{"symbols": ["🍇", "🍇", "🍇"], "prize": 50}, {"symbols": ["🍓", "🍓", "🍓"], "prize": 100}, {"symbols": ["🥝", "🥝", "🥝"], "prize": 250}]'::jsonb,
 80, 92),

('Raspa da Sorte', 'PRÊMIOS ATÉ R$10.000,00 NO PIX', 5.00, 10000.00,
 ARRAY['💎', '👑', '🎰', '🎲', '🍀'],
 '[{"symbols": ["💎", "💎", "💎"], "prize": 500}, {"symbols": ["👑", "👑", "👑"], "prize": 1000}, {"symbols": ["🍀", "🍀", "🍀"], "prize": 2500}]'::jsonb,
 80, 78),

('Mega Raspa', 'PRÊMIOS ATÉ R$25.000,00 NO PIX', 10.00, 25000.00,
 ARRAY['🔥', '⚡', '💫', '🌟', '✨'],
 '[{"symbols": ["🔥", "🔥", "🔥"], "prize": 1000}, {"symbols": ["⚡", "⚡", "⚡"], "prize": 2500}, {"symbols": ["💫", "💫", "💫"], "prize": 5000}]'::jsonb,
 80, 65),

('Super Jackpot', 'PRÊMIOS ATÉ R$50.000,00 NO PIX', 20.00, 50000.00,
 ARRAY['🎯', '🏆', '🥇', '🎖️', '🏅'],
 '[{"symbols": ["🎯", "🎯", "🎯"], "prize": 2000}, {"symbols": ["🏆", "🏆", "🏆"], "prize": 5000}, {"symbols": ["🥇", "🥇", "🥇"], "prize": 10000}]'::jsonb,
 80, 45),

('Ultra Premium', 'PRÊMIOS ATÉ R$100.000,00 NO PIX', 50.00, 100000.00,
 ARRAY['💰', '💸', '💵', '💴', '💶'],
 '[{"symbols": ["💰", "💰", "💰"], "prize": 5000}, {"symbols": ["💸", "💸", "💸"], "prize": 10000}, {"symbols": ["💵", "💵", "💵"], "prize": 25000}]'::jsonb,
 80, 25);

-- Inserir prêmios para cada jogo
DO $$
DECLARE
    game_record RECORD;
BEGIN
    FOR game_record IN SELECT id, max_prize FROM games LOOP
        INSERT INTO prizes (game_id, name, value, type, probability, winning_symbols) VALUES
        (game_record.id, 'Tente novamente', 0, 'money', 60.0, ARRAY[]::TEXT[]),
        (game_record.id, 'R$ ' || (game_record.max_prize * 0.005)::text, game_record.max_prize * 0.005, 'money', 20.0, ARRAY['🍎', '🍎', '🍎']),
        (game_record.id, 'R$ ' || (game_record.max_prize * 0.01)::text, game_record.max_prize * 0.01, 'money', 10.0, ARRAY['🍊', '🍊', '🍊']),
        (game_record.id, 'R$ ' || (game_record.max_prize * 0.025)::text, game_record.max_prize * 0.025, 'money', 5.0, ARRAY['🍋', '🍋', '🍋']),
        (game_record.id, 'R$ ' || (game_record.max_prize * 0.05)::text, game_record.max_prize * 0.05, 'money', 3.0, ARRAY['🍇', '🍇', '🍇']),
        (game_record.id, 'R$ ' || (game_record.max_prize * 0.1)::text, game_record.max_prize * 0.1, 'money', 1.5, ARRAY['🍓', '🍓', '🍓']),
        (game_record.id, 'R$ ' || (game_record.max_prize * 0.5)::text, game_record.max_prize * 0.5, 'money', 0.4, ARRAY['💎', '💎', '💎']),
        (game_record.id, 'R$ ' || game_record.max_prize::text, game_record.max_prize, 'money', 0.1, ARRAY['👑', '👑', '👑']);
    END LOOP;
END $$;

-- Views úteis para relatórios
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.name,
    u.email,
    u.balance,
    COUNT(p.id) as total_plays,
    COALESCE(SUM(p.amount_paid), 0) as total_spent,
    COALESCE(SUM(p.amount_won), 0) as total_won,
    COALESCE(SUM(p.amount_won) - SUM(p.amount_paid), 0) as net_result,
    COALESCE(AVG(p.scratch_percentage), 0) as avg_scratch_percentage
FROM users u
LEFT JOIN plays p ON u.id = p.user_id
GROUP BY u.id, u.name, u.email, u.balance;

CREATE VIEW daily_revenue AS
SELECT 
    DATE(p.played_at) as play_date,
    COUNT(*) as total_plays,
    SUM(p.amount_paid) as total_revenue,
    SUM(p.amount_won) as total_prizes,
    SUM(p.amount_paid) - SUM(p.amount_won) as net_profit,
    AVG(p.scratch_percentage) as avg_scratch_percentage
FROM plays p
WHERE p.status = 'completed'
GROUP BY DATE(p.played_at)
ORDER BY play_date DESC;

CREATE VIEW symbol_usage_stats AS
SELECT 
    ss.id,
    ss.symbol,
    ss.name,
    ss.category,
    ss.usage_count,
    COUNT(DISTINCT g.id) as games_using_symbol,
    COALESCE(SUM(p.amount_paid), 0) as total_revenue_generated
FROM scratch_symbols ss
LEFT JOIN games g ON ss.symbol = ANY(g.symbols)
LEFT JOIN plays p ON g.id = p.game_id
GROUP BY ss.id, ss.symbol, ss.name, ss.category, ss.usage_count
ORDER BY ss.usage_count DESC;

-- Função para atualizar contadores de uso de símbolos
CREATE OR REPLACE FUNCTION update_symbol_usage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE scratch_symbols 
        SET usage_count = usage_count + 1 
        WHERE symbol = ANY(NEW.revealed_symbols);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_symbol_usage_trigger
    AFTER UPDATE ON plays
    FOR EACH ROW
    EXECUTE FUNCTION update_symbol_usage();

-- Função para limpeza de sessões expiradas
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Comentários nas tabelas
COMMENT ON TABLE users IS 'Tabela de usuários do sistema';
COMMENT ON TABLE scratch_symbols IS 'Tabela de símbolos disponíveis para raspadinhas';
COMMENT ON TABLE games IS 'Tabela de jogos/raspadinhas disponíveis';
COMMENT ON TABLE prizes IS 'Tabela de prêmios possíveis para cada jogo';
COMMENT ON TABLE plays IS 'Tabela de jogadas realizadas pelos usuários';
COMMENT ON TABLE pix_transactions IS 'Tabela de transações PIX (depósitos e saques)';
COMMENT ON TABLE financial_movements IS 'Tabela de movimentações financeiras dos usuários';
COMMENT ON TABLE user_sessions IS 'Tabela de sessões ativas dos usuários';
COMMENT ON TABLE system_logs IS 'Tabela de logs de auditoria do sistema';