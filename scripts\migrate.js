const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'raspa_green',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

async function runMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Iniciando migração do banco de dados...');
    
    // Ler o arquivo de schema
    const schemaPath = path.join(__dirname, '..', 'database', 'schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    // Executar o schema
    await client.query(schemaSql);
    
    console.log('✅ Migração concluída com sucesso!');
    console.log('📊 Tabelas criadas:');
    console.log('   - users (usuários)');
    console.log('   - scratch_symbols (símbolos)');
    console.log('   - games (jogos)');
    console.log('   - prizes (prêmios)');
    console.log('   - plays (jogadas)');
    console.log('   - pix_transactions (transações PIX)');
    console.log('   - financial_movements (movimentações financeiras)');
    console.log('   - user_sessions (sessões)');
    console.log('   - system_logs (logs do sistema)');
    console.log('');
    console.log('🎯 Views criadas:');
    console.log('   - user_stats (estatísticas de usuários)');
    console.log('   - daily_revenue (receita diária)');
    console.log('   - symbol_usage_stats (estatísticas de símbolos)');
    console.log('');
    console.log('🔧 Funções e triggers criados:');
    console.log('   - update_updated_at_column()');
    console.log('   - update_symbol_usage()');
    console.log('   - cleanup_expired_sessions()');
    console.log('');
    console.log('👥 Usuários de teste criados:');
    console.log('   - <EMAIL> (senha: 123456789)');
    console.log('   - <EMAIL> (senha: 123456)');
    console.log('   - <EMAIL> (senha: 123456)');
    console.log('   - <EMAIL> (senha: 123456)');
    console.log('');
    console.log('🎰 Jogos de exemplo criados:');
    console.log('   - Raspa da Esperança (R$ 1,00)');
    console.log('   - Raspa da Alegria (R$ 2,00)');
    console.log('   - Raspa da Sorte (R$ 5,00)');
    console.log('   - Mega Raspa (R$ 10,00)');
    console.log('   - Super Jackpot (R$ 20,00)');
    console.log('   - Ultra Premium (R$ 50,00)');
    
  } catch (error) {
    console.error('❌ Erro na migração:', error.message);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Verificar se o banco existe
async function checkDatabase() {
  const adminPool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres', // Conectar ao banco padrão
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  const client = await adminPool.connect();
  
  try {
    // Verificar se o banco existe
    const result = await client.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [process.env.DB_NAME || 'raspa_green']
    );

    if (result.rows.length === 0) {
      console.log('📦 Criando banco de dados...');
      await client.query(`CREATE DATABASE ${process.env.DB_NAME || 'raspa_green'}`);
      console.log('✅ Banco de dados criado!');
    } else {
      console.log('📦 Banco de dados já existe.');
    }
  } catch (error) {
    console.error('❌ Erro ao verificar/criar banco:', error.message);
    process.exit(1);
  } finally {
    client.release();
    await adminPool.end();
  }
}

async function main() {
  console.log('🎰 RASPA GREEN - Migração do Banco de Dados');
  console.log('==========================================');
  console.log('');
  
  await checkDatabase();
  await runMigrations();
  
  console.log('🎉 Migração finalizada! O sistema está pronto para uso.');
  console.log('');
  console.log('Para iniciar o servidor:');
  console.log('  npm run dev');
  console.log('');
  console.log('Para acessar o painel admin:');
  console.log('  Email: <EMAIL>');
  console.log('  Senha: 123456789');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runMigrations, checkDatabase };
