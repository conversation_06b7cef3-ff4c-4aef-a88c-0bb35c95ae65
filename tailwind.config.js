/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./client/index.html",
    "./client/src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        gold: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        scratch: {
          overlay: '#c0c0c0',
          revealed: '#ffffff',
          winning: '#ffd700',
        }
      },
      fontFamily: {
        'scratch': ['Impact', 'Arial Black', 'sans-serif'],
      },
      animation: {
        'scratch': 'scratch 0.3s ease-in-out',
        'win': 'win 1s ease-in-out infinite',
        'pulse-gold': 'pulse-gold 2s ease-in-out infinite',
      },
      keyframes: {
        scratch: {
          '0%': { opacity: '1' },
          '50%': { opacity: '0.5' },
          '100%': { opacity: '0' }
        },
        win: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' }
        },
        'pulse-gold': {
          '0%, 100%': { 
            boxShadow: '0 0 0 0 rgba(251, 191, 36, 0.7)' 
          },
          '70%': { 
            boxShadow: '0 0 0 10px rgba(251, 191, 36, 0)' 
          }
        }
      },
      backgroundImage: {
        'scratch-pattern': "url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23c0c0c0\" fill-opacity=\"0.4\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"3\"/%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"3\"/%3E%3C/g%3E%3C/svg%3E')",
        'gradient-gold': 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%)',
        'gradient-green': 'linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%)',
      }
    },
  },
  plugins: [],
}
