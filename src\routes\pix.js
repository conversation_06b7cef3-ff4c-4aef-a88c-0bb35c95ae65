const express = require('express');
const Joi = require('joi');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const pool = require('../config/database');
const { authenticateToken, requirePermission } = require('../middleware/auth');

const router = express.Router();

// Schemas de validação
const depositSchema = Joi.object({
  amount: Joi.number().min(1).max(10000).required(),
  cpf: Joi.string().pattern(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/).required()
});

const withdrawalSchema = Joi.object({
  amount: Joi.number().min(10).max(5000).required(),
  pixKey: Joi.string().required(),
  cpf: Joi.string().pattern(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/).required()
});

// Função para gerar QR Code PIX (simulação)
const generatePixQRCode = async (amount, transactionId) => {
  // Em produção, aqui seria integrado com um gateway PIX real
  // Por enquanto, retornamos dados simulados
  return {
    qrCode: `00020126580014BR.GOV.BCB.PIX0136${transactionId}520400005303986540${amount.toFixed(2)}5802BR5925RASPA GREEN LTDA6009SAO PAULO62070503***6304`,
    pixCopyPaste: `PIX_COPY_PASTE_${transactionId}`,
    expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutos
  };
};

// Função para processar saque PIX (simulação)
const processPixWithdrawal = async (amount, pixKey, cpf, transactionId) => {
  // Em produção, aqui seria integrado com um gateway PIX real
  // Por enquanto, simulamos o processamento
  return new Promise((resolve) => {
    setTimeout(() => {
      // Simula 90% de sucesso
      const success = Math.random() > 0.1;
      resolve({
        success,
        transactionId,
        message: success ? 'Saque processado com sucesso' : 'Falha no processamento do saque'
      });
    }, 2000);
  });
};

// Criar depósito PIX
router.post('/deposit', authenticateToken, requirePermission('deposit'), async (req, res, next) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const { error, value } = depositSchema.validate(req.body);
    if (error) throw error;

    const userId = req.user.id;
    const { amount, cpf } = value;
    const transactionId = uuidv4();

    // Gerar QR Code PIX
    const pixData = await generatePixQRCode(amount, transactionId);

    // Criar transação no banco
    const result = await client.query(`
      INSERT INTO pix_transactions (
        user_id, transaction_id, gateway, type, amount, status, 
        qr_code, cpf, expires_at
      ) VALUES ($1, $2, 'SIMULATED', 'deposit', $3, 'pending', $4, $5, $6)
      RETURNING *
    `, [userId, transactionId, amount, pixData.qrCode, cpf, pixData.expiresAt]);

    await client.query('COMMIT');

    res.json({
      message: 'Depósito PIX criado com sucesso',
      transaction: {
        id: result.rows[0].id,
        transactionId: result.rows[0].transaction_id,
        amount: result.rows[0].amount,
        status: result.rows[0].status,
        qrCode: result.rows[0].qr_code,
        pixCopyPaste: pixData.pixCopyPaste,
        expiresAt: result.rows[0].expires_at
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    next(error);
  } finally {
    client.release();
  }
});

// Criar saque PIX
router.post('/withdrawal', authenticateToken, requirePermission('withdraw'), async (req, res, next) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const { error, value } = withdrawalSchema.validate(req.body);
    if (error) throw error;

    const userId = req.user.id;
    const { amount, pixKey, cpf } = value;

    // Verificar saldo do usuário
    const userResult = await client.query(
      'SELECT balance FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      throw new Error('Usuário não encontrado');
    }

    const userBalance = parseFloat(userResult.rows[0].balance);
    if (userBalance < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente' });
    }

    const transactionId = uuidv4();

    // Criar transação no banco
    const transactionResult = await client.query(`
      INSERT INTO pix_transactions (
        user_id, transaction_id, gateway, type, amount, status, 
        pix_key, cpf
      ) VALUES ($1, $2, 'SIMULATED', 'withdrawal', $3, 'pending', $4, $5)
      RETURNING *
    `, [userId, transactionId, amount, pixKey, cpf]);

    // Debitar valor do saldo do usuário
    await client.query(
      'UPDATE users SET balance = balance - $1 WHERE id = $2',
      [amount, userId]
    );

    // Registrar movimentação financeira
    await client.query(`
      INSERT INTO financial_movements (
        user_id, type, amount, balance_before, balance_after, 
        reference_id, reference_type, description
      ) VALUES ($1, 'withdrawal', $2, $3, $4, $5, 'pix_transaction', $6)
    `, [
      userId, 
      amount, 
      userBalance, 
      userBalance - amount, 
      transactionResult.rows[0].id,
      `Saque PIX - ${pixKey}`
    ]);

    await client.query('COMMIT');

    // Processar saque de forma assíncrona
    processPixWithdrawal(amount, pixKey, cpf, transactionId)
      .then(async (result) => {
        const status = result.success ? 'completed' : 'failed';
        const completedAt = result.success ? new Date() : null;

        await pool.query(`
          UPDATE pix_transactions 
          SET status = $1, completed_at = $2, webhook_data = $3
          WHERE transaction_id = $4
        `, [status, completedAt, JSON.stringify(result), transactionId]);

        // Se falhou, estornar o valor
        if (!result.success) {
          const client2 = await pool.connect();
          try {
            await client2.query('BEGIN');
            
            await client2.query(
              'UPDATE users SET balance = balance + $1 WHERE id = $2',
              [amount, userId]
            );

            await client2.query(`
              INSERT INTO financial_movements (
                user_id, type, amount, balance_before, balance_after, 
                reference_id, reference_type, description
              ) VALUES ($1, 'refund', $2, $3, $4, $5, 'pix_transaction', $6)
            `, [
              userId, 
              amount, 
              userBalance - amount, 
              userBalance, 
              transactionResult.rows[0].id,
              `Estorno - Falha no saque PIX`
            ]);

            await client2.query('COMMIT');
          } catch (err) {
            await client2.query('ROLLBACK');
            console.error('Erro ao estornar saque:', err);
          } finally {
            client2.release();
          }
        }
      })
      .catch(err => {
        console.error('Erro no processamento do saque:', err);
      });

    res.json({
      message: 'Saque PIX iniciado com sucesso',
      transaction: {
        id: transactionResult.rows[0].id,
        transactionId: transactionResult.rows[0].transaction_id,
        amount: transactionResult.rows[0].amount,
        status: transactionResult.rows[0].status,
        pixKey: transactionResult.rows[0].pix_key
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    next(error);
  } finally {
    client.release();
  }
});

// Listar transações PIX do usuário
router.get('/transactions', authenticateToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const type = req.query.type; // deposit, withdrawal
    const status = req.query.status; // pending, completed, failed, cancelled
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE user_id = $1';
    const queryParams = [userId];
    let paramCount = 2;

    if (type) {
      whereClause += ` AND type = $${paramCount}`;
      queryParams.push(type);
      paramCount++;
    }

    if (status) {
      whereClause += ` AND status = $${paramCount}`;
      queryParams.push(status);
      paramCount++;
    }

    const result = await pool.query(`
      SELECT 
        id, transaction_id, type, amount, status, qr_code, pix_key, 
        created_at, completed_at, expires_at
      FROM pix_transactions 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...queryParams, limit, offset]);

    const countResult = await pool.query(`
      SELECT COUNT(*) FROM pix_transactions ${whereClause}
    `, queryParams);

    const totalCount = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      transactions: result.rows,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    next(error);
  }
});

// Verificar status de transação PIX
router.get('/transaction/:transactionId', authenticateToken, async (req, res, next) => {
  try {
    const { transactionId } = req.params;
    const userId = req.user.id;

    const result = await pool.query(`
      SELECT * FROM pix_transactions 
      WHERE transaction_id = $1 AND user_id = $2
    `, [transactionId, userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    next(error);
  }
});

// Webhook para receber notificações do gateway PIX (simulação)
router.post('/webhook', async (req, res, next) => {
  try {
    const { transactionId, status, amount } = req.body;

    // Verificar se a transação existe
    const transactionResult = await pool.query(
      'SELECT * FROM pix_transactions WHERE transaction_id = $1',
      [transactionId]
    );

    if (transactionResult.rows.length === 0) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    const transaction = transactionResult.rows[0];

    // Atualizar status da transação
    await pool.query(`
      UPDATE pix_transactions 
      SET status = $1, completed_at = $2, webhook_data = $3
      WHERE transaction_id = $4
    `, [status, new Date(), JSON.stringify(req.body), transactionId]);

    // Se é um depósito aprovado, creditar valor na conta
    if (transaction.type === 'deposit' && status === 'completed') {
      const client = await pool.connect();
      try {
        await client.query('BEGIN');

        // Obter saldo atual
        const userResult = await client.query(
          'SELECT balance FROM users WHERE id = $1',
          [transaction.user_id]
        );

        const currentBalance = parseFloat(userResult.rows[0].balance);

        // Creditar valor
        await client.query(
          'UPDATE users SET balance = balance + $1 WHERE id = $2',
          [amount, transaction.user_id]
        );

        // Registrar movimentação financeira
        await client.query(`
          INSERT INTO financial_movements (
            user_id, type, amount, balance_before, balance_after, 
            reference_id, reference_type, description
          ) VALUES ($1, 'deposit', $2, $3, $4, $5, 'pix_transaction', $6)
        `, [
          transaction.user_id, 
          amount, 
          currentBalance, 
          currentBalance + amount, 
          transaction.id,
          `Depósito PIX aprovado`
        ]);

        await client.query('COMMIT');
      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }
    }

    res.json({ message: 'Webhook processado com sucesso' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
