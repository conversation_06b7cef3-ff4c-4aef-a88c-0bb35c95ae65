{"version": 3, "file": "socket.io.msgpack.min.js", "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/index.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../socket.io-component-emitter/lib/esm/index.js", "../../engine.io-client/build/esm/globals.js", "../../engine.io-client/build/esm/util.js", "../../engine.io-client/build/esm/transport.js", "../../engine.io-client/build/esm/contrib/parseqs.js", "../../engine.io-client/build/esm/transports/polling.js", "../../engine.io-client/build/esm/contrib/has-cors.js", "../../engine.io-client/build/esm/transports/polling-xhr.js", "../../engine.io-client/build/esm/transports/websocket.js", "../../engine.io-client/build/esm/transports/webtransport.js", "../../engine.io-client/build/esm/transports/index.js", "../../engine.io-client/build/esm/contrib/parseuri.js", "../../engine.io-client/build/esm/socket.js", "../../engine.io-client/build/esm/index.js", "../../../node_modules/notepack.io/browser/encode.js", "../../../node_modules/notepack.io/browser/decode.js", "../../../node_modules/notepack.io/lib/index.js", "../../../node_modules/component-emitter/index.js", "../../../node_modules/socket.io-msgpack-parser/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n", "'use strict';\n\nfunction utf8Write(view, offset, str) {\n  var c = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      view.setUint8(offset++, c);\n    }\n    else if (c < 0x800) {\n      view.setUint8(offset++, 0xc0 | (c >> 6));\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      view.setUint8(offset++, 0xe0 | (c >> 12));\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else {\n      i++;\n      c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n      view.setUint8(offset++, 0xf0 | (c >> 18));\n      view.setUint8(offset++, 0x80 | (c >> 12) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n  }\n}\n\nfunction utf8Length(str) {\n  var c = 0, length = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    }\n    else if (c < 0x800) {\n      length += 2;\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    }\n    else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n\nfunction _encode(bytes, defers, value) {\n  var type = typeof value, i = 0, l = 0, hi = 0, lo = 0, length = 0, size = 0;\n\n  if (type === 'string') {\n    length = utf8Length(value);\n\n    // fixstr\n    if (length < 0x20) {\n      bytes.push(length | 0xa0);\n      size = 1;\n    }\n    // str 8\n    else if (length < 0x100) {\n      bytes.push(0xd9, length);\n      size = 2;\n    }\n    // str 16\n    else if (length < 0x10000) {\n      bytes.push(0xda, length >> 8, length);\n      size = 3;\n    }\n    // str 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdb, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('String too long');\n    }\n    defers.push({ _str: value, _length: length, _offset: bytes.length });\n    return size + length;\n  }\n  if (type === 'number') {\n    // TODO: encode to float 32?\n\n    // float 64\n    if (Math.floor(value) !== value || !isFinite(value)) {\n      bytes.push(0xcb);\n      defers.push({ _float: value, _length: 8, _offset: bytes.length });\n      return 9;\n    }\n\n    if (value >= 0) {\n      // positive fixnum\n      if (value < 0x80) {\n        bytes.push(value);\n        return 1;\n      }\n      // uint 8\n      if (value < 0x100) {\n        bytes.push(0xcc, value);\n        return 2;\n      }\n      // uint 16\n      if (value < 0x10000) {\n        bytes.push(0xcd, value >> 8, value);\n        return 3;\n      }\n      // uint 32\n      if (value < 0x100000000) {\n        bytes.push(0xce, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // uint 64\n      hi = (value / Math.pow(2, 32)) >> 0;\n      lo = value >>> 0;\n      bytes.push(0xcf, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    } else {\n      // negative fixnum\n      if (value >= -0x20) {\n        bytes.push(value);\n        return 1;\n      }\n      // int 8\n      if (value >= -0x80) {\n        bytes.push(0xd0, value);\n        return 2;\n      }\n      // int 16\n      if (value >= -0x8000) {\n        bytes.push(0xd1, value >> 8, value);\n        return 3;\n      }\n      // int 32\n      if (value >= -0x80000000) {\n        bytes.push(0xd2, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // int 64\n      hi = Math.floor(value / Math.pow(2, 32));\n      lo = value >>> 0;\n      bytes.push(0xd3, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    }\n  }\n  if (type === 'object') {\n    // nil\n    if (value === null) {\n      bytes.push(0xc0);\n      return 1;\n    }\n\n    if (Array.isArray(value)) {\n      length = value.length;\n\n      // fixarray\n      if (length < 0x10) {\n        bytes.push(length | 0x90);\n        size = 1;\n      }\n      // array 16\n      else if (length < 0x10000) {\n        bytes.push(0xdc, length >> 8, length);\n        size = 3;\n      }\n      // array 32\n      else if (length < 0x100000000) {\n        bytes.push(0xdd, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Array too large');\n      }\n      for (i = 0; i < length; i++) {\n        size += _encode(bytes, defers, value[i]);\n      }\n      return size;\n    }\n\n    // fixext 8 / Date\n    if (value instanceof Date) {\n      var time = value.getTime();\n      hi = Math.floor(time / Math.pow(2, 32));\n      lo = time >>> 0;\n      bytes.push(0xd7, 0, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 10;\n    }\n\n    if (value instanceof ArrayBuffer) {\n      length = value.byteLength;\n\n      // bin 8\n      if (length < 0x100) {\n        bytes.push(0xc4, length);\n        size = 2;\n      } else\n      // bin 16\n      if (length < 0x10000) {\n        bytes.push(0xc5, length >> 8, length);\n        size = 3;\n      } else\n      // bin 32\n      if (length < 0x100000000) {\n        bytes.push(0xc6, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Buffer too large');\n      }\n      defers.push({ _bin: value, _length: length, _offset: bytes.length });\n      return size + length;\n    }\n\n    if (typeof value.toJSON === 'function') {\n      return _encode(bytes, defers, value.toJSON());\n    }\n\n    var keys = [], key = '';\n\n    var allKeys = Object.keys(value);\n    for (i = 0, l = allKeys.length; i < l; i++) {\n      key = allKeys[i];\n      if (typeof value[key] !== 'function') {\n        keys.push(key);\n      }\n    }\n    length = keys.length;\n\n    // fixmap\n    if (length < 0x10) {\n      bytes.push(length | 0x80);\n      size = 1;\n    }\n    // map 16\n    else if (length < 0x10000) {\n      bytes.push(0xde, length >> 8, length);\n      size = 3;\n    }\n    // map 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdf, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('Object too large');\n    }\n\n    for (i = 0; i < length; i++) {\n      key = keys[i];\n      size += _encode(bytes, defers, key);\n      size += _encode(bytes, defers, value[key]);\n    }\n    return size;\n  }\n  // false/true\n  if (type === 'boolean') {\n    bytes.push(value ? 0xc3 : 0xc2);\n    return 1;\n  }\n  // fixext 1 / undefined\n  if (type === 'undefined') {\n    bytes.push(0xd4, 0, 0);\n    return 3;\n  }\n  throw new Error('Could not encode');\n}\n\nfunction encode(value) {\n  var bytes = [];\n  var defers = [];\n  var size = _encode(bytes, defers, value);\n  var buf = new ArrayBuffer(size);\n  var view = new DataView(buf);\n\n  var deferIndex = 0;\n  var deferWritten = 0;\n  var nextOffset = -1;\n  if (defers.length > 0) {\n    nextOffset = defers[0]._offset;\n  }\n\n  var defer, deferLength = 0, offset = 0;\n  for (var i = 0, l = bytes.length; i < l; i++) {\n    view.setUint8(deferWritten + i, bytes[i]);\n    if (i + 1 !== nextOffset) { continue; }\n    defer = defers[deferIndex];\n    deferLength = defer._length;\n    offset = deferWritten + nextOffset;\n    if (defer._bin) {\n      var bin = new Uint8Array(defer._bin);\n      for (var j = 0; j < deferLength; j++) {\n        view.setUint8(offset + j, bin[j]);\n      }\n    } else if (defer._str) {\n      utf8Write(view, offset, defer._str);\n    } else if (defer._float !== undefined) {\n      view.setFloat64(offset, defer._float);\n    }\n    deferIndex++;\n    deferWritten += deferLength;\n    if (defers[deferIndex]) {\n      nextOffset = defers[deferIndex]._offset;\n    }\n  }\n  return buf;\n}\n\nmodule.exports = encode;\n", "'use strict';\n\nfunction Decoder(buffer) {\n  this._offset = 0;\n  if (buffer instanceof ArrayBuffer) {\n    this._buffer = buffer;\n    this._view = new DataView(this._buffer);\n  } else if (ArrayBuffer.isView(buffer)) {\n    this._buffer = buffer.buffer;\n    this._view = new DataView(this._buffer, buffer.byteOffset, buffer.byteLength);\n  } else {\n    throw new Error('Invalid argument');\n  }\n}\n\nfunction utf8Read(view, offset, length) {\n  var string = '', chr = 0;\n  for (var i = offset, end = offset + length; i < end; i++) {\n    var byte = view.getUint8(i);\n    if ((byte & 0x80) === 0x00) {\n      string += String.fromCharCode(byte);\n      continue;\n    }\n    if ((byte & 0xe0) === 0xc0) {\n      string += String.fromCharCode(\n        ((byte & 0x1f) << 6) |\n        (view.getUint8(++i) & 0x3f)\n      );\n      continue;\n    }\n    if ((byte & 0xf0) === 0xe0) {\n      string += String.fromCharCode(\n        ((byte & 0x0f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0)\n      );\n      continue;\n    }\n    if ((byte & 0xf8) === 0xf0) {\n      chr = ((byte & 0x07) << 18) |\n        ((view.getUint8(++i) & 0x3f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0);\n      if (chr >= 0x010000) { // surrogate pair\n        chr -= 0x010000;\n        string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n      } else {\n        string += String.fromCharCode(chr);\n      }\n      continue;\n    }\n    throw new Error('Invalid byte ' + byte.toString(16));\n  }\n  return string;\n}\n\nDecoder.prototype._array = function (length) {\n  var value = new Array(length);\n  for (var i = 0; i < length; i++) {\n    value[i] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._map = function (length) {\n  var key = '', value = {};\n  for (var i = 0; i < length; i++) {\n    key = this._parse();\n    value[key] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._str = function (length) {\n  var value = utf8Read(this._view, this._offset, length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._bin = function (length) {\n  var value = this._buffer.slice(this._offset, this._offset + length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._parse = function () {\n  var prefix = this._view.getUint8(this._offset++);\n  var value, length = 0, type = 0, hi = 0, lo = 0;\n\n  if (prefix < 0xc0) {\n    // positive fixint\n    if (prefix < 0x80) {\n      return prefix;\n    }\n    // fixmap\n    if (prefix < 0x90) {\n      return this._map(prefix & 0x0f);\n    }\n    // fixarray\n    if (prefix < 0xa0) {\n      return this._array(prefix & 0x0f);\n    }\n    // fixstr\n    return this._str(prefix & 0x1f);\n  }\n\n  // negative fixint\n  if (prefix > 0xdf) {\n    return (0xff - prefix + 1) * -1;\n  }\n\n  switch (prefix) {\n    // nil\n    case 0xc0:\n      return null;\n    // false\n    case 0xc2:\n      return false;\n    // true\n    case 0xc3:\n      return true;\n\n    // bin\n    case 0xc4:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._bin(length);\n    case 0xc5:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._bin(length);\n    case 0xc6:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._bin(length);\n\n    // ext\n    case 0xc7:\n      length = this._view.getUint8(this._offset);\n      type = this._view.getInt8(this._offset + 1);\n      this._offset += 2;\n      return [type, this._bin(length)];\n    case 0xc8:\n      length = this._view.getUint16(this._offset);\n      type = this._view.getInt8(this._offset + 2);\n      this._offset += 3;\n      return [type, this._bin(length)];\n    case 0xc9:\n      length = this._view.getUint32(this._offset);\n      type = this._view.getInt8(this._offset + 4);\n      this._offset += 5;\n      return [type, this._bin(length)];\n\n    // float\n    case 0xca:\n      value = this._view.getFloat32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcb:\n      value = this._view.getFloat64(this._offset);\n      this._offset += 8;\n      return value;\n\n    // uint\n    case 0xcc:\n      value = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xcd:\n      value = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xce:\n      value = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcf:\n      hi = this._view.getUint32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // int\n    case 0xd0:\n      value = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xd1:\n      value = this._view.getInt16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xd2:\n      value = this._view.getInt32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xd3:\n      hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // fixext\n    case 0xd4:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        this._offset += 1;\n        return void 0;\n      }\n      return [type, this._bin(1)];\n    case 0xd5:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(2)];\n    case 0xd6:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(4)];\n    case 0xd7:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n        lo = this._view.getUint32(this._offset + 4);\n        this._offset += 8;\n        return new Date(hi + lo);\n      }\n      return [type, this._bin(8)];\n    case 0xd8:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(16)];\n\n    // str\n    case 0xd9:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._str(length);\n    case 0xda:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._str(length);\n    case 0xdb:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._str(length);\n\n    // array\n    case 0xdc:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._array(length);\n    case 0xdd:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._array(length);\n\n    // map\n    case 0xde:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._map(length);\n    case 0xdf:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._map(length);\n  }\n\n  throw new Error('Could not parse');\n};\n\nfunction decode(buffer) {\n  var decoder = new Decoder(buffer);\n  var value = decoder._parse();\n  if (decoder._offset !== buffer.byteLength) {\n    throw new Error((buffer.byteLength - decoder._offset) + ' trailing bytes');\n  }\n  return value;\n}\n\nmodule.exports = decode;\n", "exports.encode = require('./encode');\nexports.decode = require('./decode');\n", "\n/**\n * Expose `Emitter`.\n */\n\nif (typeof module !== 'undefined') {\n  module.exports = Emitter;\n}\n\n/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nfunction Emitter(obj) {\n  if (obj) return mixin(obj);\n};\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "var msgpack = require(\"notepack.io\");\nvar Emitter = require(\"component-emitter\");\n\nexports.protocol = 5;\n\n/**\n * Packet types (see https://github.com/socketio/socket.io-protocol)\n */\n\nvar PacketType = (exports.PacketType = {\n  CONNECT: 0,\n  DISCONNECT: 1,\n  EVENT: 2,\n  ACK: 3,\n  CONNECT_ERROR: 4,\n});\n\nvar isInteger =\n  Number.isInteger ||\n  function (value) {\n    return (\n      typeof value === \"number\" &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    );\n  };\n\nvar isString = function (value) {\n  return typeof value === \"string\";\n};\n\nvar isObject = function (value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n};\n\nfunction Encoder() {}\n\nEncoder.prototype.encode = function (packet) {\n  return [msgpack.encode(packet)];\n};\n\nfunction Decoder() {}\n\nEmitter(Decoder.prototype);\n\nDecoder.prototype.add = function (obj) {\n  var decoded = msgpack.decode(obj);\n  this.checkPacket(decoded);\n  this.emit(\"decoded\", decoded);\n};\n\nfunction isDataValid(decoded) {\n  switch (decoded.type) {\n    case PacketType.CONNECT:\n      return decoded.data === undefined || isObject(decoded.data);\n    case PacketType.DISCONNECT:\n      return decoded.data === undefined;\n    case PacketType.CONNECT_ERROR:\n      return isString(decoded.data) || isObject(decoded.data);\n    default:\n      return Array.isArray(decoded.data);\n  }\n}\n\nDecoder.prototype.checkPacket = function (decoded) {\n  var isTypeValid =\n    isInteger(decoded.type) &&\n    decoded.type >= PacketType.CONNECT &&\n    decoded.type <= PacketType.CONNECT_ERROR;\n  if (!isTypeValid) {\n    throw new Error(\"invalid packet type\");\n  }\n\n  if (!isString(decoded.nsp)) {\n    throw new Error(\"invalid namespace\");\n  }\n\n  if (!isDataValid(decoded)) {\n    throw new Error(\"invalid payload\");\n  }\n\n  var isAckValid = decoded.id === undefined || isInteger(decoded.id);\n  if (!isAckValid) {\n    throw new Error(\"invalid packet id\");\n  }\n};\n\nDecoder.prototype.destroy = function () {};\n\nexports.Encoder = Encoder;\nexports.Decoder = Decoder;\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "TEXT_ENCODER", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "chars", "lookup", "i", "charCodeAt", "TEXT_DECODER", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "header", "payloadLength", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "Emitter$1", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "randomString", "Date", "now", "Math", "random", "TransportError", "_Error", "reason", "description", "context", "_this", "_inherits<PERSON><PERSON>e", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_this2", "writable", "query", "socket", "forceBase64", "_proto", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "Polling", "_Transport", "_polling", "_poll", "total", "doPoll", "_this3", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "_this4", "_this5", "count", "join", "encodePayload", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "_createClass", "get", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "_Polling", "location", "isSSL", "protocol", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "_create", "_proto2", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "document", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "hasXHR2", "newRequest", "responseType", "XHR", "_BaseXHR", "_this6", "_extends", "concat", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_BaseWS", "_packet", "WT", "_transport", "WebTransport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "polling", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "_typeof", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "createCookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "JSON", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "_hasPingExpired", "hasExpired", "msg", "options", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_SocketWithoutUpgrade", "_this7", "_upgrades", "_probe", "_this8", "failed", "onTransportOpen", "cleanup", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "_SocketWithUpgrade", "o", "map", "DEFAULT_TRANSPORTS", "filter", "utf8Write", "offset", "_encode", "defers", "hi", "lo", "_str", "_length", "_offset", "floor", "isFinite", "_float", "isArray", "time", "getTime", "_bin", "toJSON", "allKeys", "encode_1", "buf", "deferIndex", "defer<PERSON><PERSON>ten", "nextOffset", "defer", "deferL<PERSON>th", "bin", "setFloat64", "Decoder", "_buffer", "_view", "_array", "_parse", "_map", "string", "chr", "end", "byte", "getUint8", "utf8Read", "prefix", "getInt8", "getFloat32", "getFloat64", "getInt16", "getInt32", "decode_1", "decoder", "lib", "require$$0", "require$$1", "module", "exports", "msgpack", "socket_ioMsgpackParser", "PacketType", "PacketType_1", "CONNECT", "DISCONNECT", "EVENT", "ACK", "CONNECT_ERROR", "isInteger", "isString", "isObject", "Encoder", "add", "checkPacket", "nsp", "isDataValid", "destroy", "Encoder_1", "Decoder_1", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_autoConnect", "subEvents", "subs", "onpacket", "_readyState", "unshift", "_b", "_c", "_len2", "_key2", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "isConnected", "notifyOutgoingListeners", "ackTimeout", "timer", "_len3", "_key3", "with<PERSON><PERSON><PERSON>", "emitWithAck", "_len4", "_key4", "reject", "arg1", "arg2", "tryCount", "pending", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_sendConnectPacket", "_pid", "pid", "_lastOffset", "_clearAcks", "some", "onconnect", "BINARY_EVENT", "onevent", "BINARY_ACK", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "_step", "_iterator", "_createForOfIteratorHelper", "s", "f", "sent", "_len6", "_key6", "emitBuffered", "subDestroy", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "_step2", "_iterator2", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "autoConnect", "v", "_reconnection", "skipReconnect", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "active", "_destroy", "_i", "_nsps", "_close", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;2sHA<PERSON>,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAACC,GAC/BH,EAAqBH,EAAaM,IAAQA,CAC9C,IACA,ICuCIC,EDvCEC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCX,OAAOY,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAAS,SAACC,GACZ,MAAqC,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,WACvC,EACMI,EAAe,SAAHC,EAAoBC,EAAgBC,GAAa,IAA3Cf,EAAIa,EAAJb,KAAMC,EAAIY,EAAJZ,KAC1B,OAAIC,GAAkBD,aAAgBE,KAC9BW,EACOC,EAASd,GAGTe,EAAmBf,EAAMc,GAG/BR,IACJN,aAAgBO,aAAeC,EAAOR,IACnCa,EACOC,EAASd,GAGTe,EAAmB,IAAIb,KAAK,CAACF,IAAQc,GAI7CA,EAASxB,EAAaS,IAASC,GAAQ,IAClD,EACMe,EAAqB,SAACf,EAAMc,GAC9B,IAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,IAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,MAExBH,EAAWM,cAActB,EACpC,EACA,SAASuB,EAAQvB,GACb,OAAIA,aAAgBwB,WACTxB,EAEFA,aAAgBO,YACd,IAAIiB,WAAWxB,GAGf,IAAIwB,WAAWxB,EAAKU,OAAQV,EAAKyB,WAAYzB,EAAK0B,WAEjE,CC9CA,IAHA,IAAMC,EAAQ,mEAERC,EAA+B,oBAAfJ,WAA6B,GAAK,IAAIA,WAAW,KAC9DK,EAAI,EAAGA,EAAIF,GAAcE,IAC9BD,EAAOD,EAAMG,WAAWD,IAAMA,EAkB3B,ICyCHE,EC9DEzB,EAA+C,mBAAhBC,YACxByB,EAAe,SAACC,EAAeC,GACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHlC,KAAM,UACNC,KAAMmC,EAAUF,EAAeC,IAGvC,IAAMnC,EAAOkC,EAAcG,OAAO,GAClC,MAAa,MAATrC,EACO,CACHA,KAAM,UACNC,KAAMqC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CzC,EAAqBM,GAIjCkC,EAAcM,OAAS,EACxB,CACExC,KAAMN,EAAqBM,GAC3BC,KAAMiC,EAAcK,UAAU,IAEhC,CACEvC,KAAMN,EAAqBM,IARxBD,CAUf,EACMuC,EAAqB,SAACrC,EAAMkC,GAC9B,GAAI5B,EAAuB,CACvB,IAAMkC,EFTQ,SAACC,GACnB,IAA8DZ,EAAUa,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,IAAMG,EAAc,IAAI1C,YAAYuC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKpB,EAAI,EAAGA,EAAIkB,EAAKlB,GAAK,EACtBa,EAAWd,EAAOa,EAAOX,WAAWD,IACpCc,EAAWf,EAAOa,EAAOX,WAAWD,EAAI,IACxCe,EAAWhB,EAAOa,EAAOX,WAAWD,EAAI,IACxCgB,EAAWjB,EAAOa,EAAOX,WAAWD,EAAI,IACxCqB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACX,CEVwBE,CAAOnD,GACvB,OAAOmC,EAAUK,EAASN,EAC9B,CAEI,MAAO,CAAEO,QAAQ,EAAMzC,KAAAA,EAE/B,EACMmC,EAAY,SAACnC,EAAMkC,GACrB,MACS,SADDA,EAEIlC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,MAG5B,ED1DM0C,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,UAASA,SAACC,EAAQC,IFmBnB,SAA8BD,EAAQ5C,GACrCb,GAAkByD,EAAO1D,gBAAgBE,KAClCwD,EAAO1D,KAAK4D,cAAcC,KAAKtC,GAASsC,KAAK/C,GAE/CR,IACJoD,EAAO1D,gBAAgBO,aAAeC,EAAOkD,EAAO1D,OAC9Cc,EAASS,EAAQmC,EAAO1D,OAEnCW,EAAa+C,GAAQ,GAAO,SAACI,GACpBjE,IACDA,EAAe,IAAIkE,aAEvBjD,EAASjB,EAAamE,OAAOF,GACjC,GACJ,CEhCYG,CAAqBP,GAAQ,SAACzB,GAC1B,IACIiC,EADEC,EAAgBlC,EAAcM,OAGpC,GAAI4B,EAAgB,IAChBD,EAAS,IAAI1C,WAAW,GACxB,IAAI4C,SAASF,EAAOxD,QAAQ2D,SAAS,EAAGF,QAEvC,GAAIA,EAAgB,MAAO,CAC5BD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGJ,EACtB,KACK,CACDD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAON,GAChC,CAEIT,EAAO1D,MAA+B,iBAAhB0D,EAAO1D,OAC7BkE,EAAO,IAAM,KAEjBP,EAAWe,QAAQR,GACnBP,EAAWe,QAAQzC,EACvB,GACJ,GAER,CAEA,SAAS0C,EAAYC,GACjB,OAAOA,EAAOC,QAAO,SAACC,EAAKC,GAAK,OAAKD,EAAMC,EAAMxC,MAAM,GAAE,EAC7D,CACA,SAASyC,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGrC,SAAW0C,EACrB,OAAOL,EAAOM,QAIlB,IAFA,IAAMxE,EAAS,IAAIc,WAAWyD,GAC1BE,EAAI,EACCtD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGrC,SAChBqC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOrC,QAAU4C,EAAIP,EAAO,GAAGrC,SAC/BqC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CE/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIb,KAAOyF,EAAQlF,UACtBM,EAAIb,GAAOyF,EAAQlF,UAAUP,GAE/B,OAAOa,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,GACpCD,KAAKC,EAAW,IAAMH,GAASE,KAAKC,EAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYOG,EAAC3F,UAAU4F,KAAO,SAASN,EAAOC,GACvC,SAASH,IACPI,KAAKK,IAAIP,EAAOF,GAChBG,EAAGO,MAAMN,KAAMO,UACjB,CAIA,OAFAX,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYOG,EAAC3F,UAAU6F,IAClBX,EAAQlF,UAAUgG,eAClBd,EAAQlF,UAAUiG,mBAClBf,EAAQlF,UAAUkG,oBAAsB,SAASZ,EAAOC,GAItD,GAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAGjC,GAAKM,UAAU3D,OAEjB,OADAoD,KAAKC,EAAa,GACXD,KAIT,IAUIW,EAVAC,EAAYZ,KAAKC,EAAW,IAAMH,GACtC,IAAKc,EAAW,OAAOZ,KAGvB,GAAI,GAAKO,UAAU3D,OAEjB,cADOoD,KAAKC,EAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAI0E,EAAUhE,OAAQV,IAEpC,IADAyE,EAAKC,EAAU1E,MACJ6D,GAAMY,EAAGZ,KAAOA,EAAI,CAC7Ba,EAAUC,OAAO3E,EAAG,GACpB,KACF,CASF,OAJyB,IAArB0E,EAAUhE,eACLoD,KAAKC,EAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUsG,KAAO,SAAShB,GAChCE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAKrC,IAHA,IAAIc,EAAO,IAAIC,MAAMT,UAAU3D,OAAS,GACpCgE,EAAYZ,KAAKC,EAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIqE,UAAU3D,OAAQV,IACpC6E,EAAK7E,EAAI,GAAKqE,UAAUrE,GAG1B,GAAI0E,EAEG,CAAI1E,EAAI,EAAb,IAAK,IAAWkB,GADhBwD,EAAYA,EAAUnB,MAAM,IACI7C,OAAQV,EAAIkB,IAAOlB,EACjD0E,EAAU1E,GAAGoE,MAAMN,KAAMe,EADKnE,CAKlC,OAAOoD,IACT,EAGOG,EAAC3F,UAAUyG,aAAevB,EAAQlF,UAAUsG,KAUnDpB,EAAQlF,UAAU0G,UAAY,SAASpB,GAErC,OADAE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAC9BD,KAAKC,EAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU2G,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAOlD,MAClC,ECxKO,IAAMwE,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAACX,GAAE,OAAKU,QAAQC,UAAUpD,KAAKyC,EAAG,EAGlC,SAACA,EAAIY,GAAY,OAAKA,EAAaZ,EAAI,EAAE,EAG3Ca,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GChBR,SAASC,EAAK9G,GAAc,IAAA+G,IAAAA,EAAAtB,UAAA3D,OAANkF,MAAId,MAAAa,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,EAAAxB,GAAAA,UAAAwB,GAC7B,OAAOD,EAAK5C,QAAO,SAACC,EAAK6C,GAIrB,OAHIlH,EAAImH,eAAeD,KACnB7C,EAAI6C,GAAKlH,EAAIkH,IAEV7C,CACV,GAAE,CAAE,EACT,CAEA,IAAM+C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBzH,EAAK0H,GACnCA,EAAKC,iBACL3H,EAAIyG,aAAeW,EAAmBQ,KAAKP,GAC3CrH,EAAI6H,eAAiBN,EAAqBK,KAAKP,KAG/CrH,EAAIyG,aAAeY,EAAWC,WAAWM,KAAKP,GAC9CrH,EAAI6H,eAAiBR,EAAWG,aAAaI,KAAKP,GAE1D,CAkCO,SAASS,IACZ,OAAQC,KAAKC,MAAMrI,SAAS,IAAIkC,UAAU,GACtCoG,KAAKC,SAASvI,SAAS,IAAIkC,UAAU,EAAG,EAChD,CCtDasG,IAAAA,WAAcC,GACvB,SAAAD,EAAYE,EAAQC,EAAaC,GAAS,IAAAC,EAIT,OAH7BA,EAAAJ,EAAAxI,KAAAsF,KAAMmD,IAAOnD,MACRoD,YAAcA,EACnBE,EAAKD,QAAUA,EACfC,EAAKlJ,KAAO,iBAAiBkJ,CACjC,CAAC,OAAAC,EAAAN,EAAAC,GAAAD,CAAA,EAAAO,EAN+BC,QAQvBC,WAASC,GAOlB,SAAAD,EAAYlB,GAAM,IAAAoB,EAO0B,OANxCA,EAAAD,EAAAjJ,YAAOsF,MACF6D,UAAW,EAChBtB,EAAqBqB,EAAOpB,GAC5BoB,EAAKpB,KAAOA,EACZoB,EAAKE,MAAQtB,EAAKsB,MAClBF,EAAKG,OAASvB,EAAKuB,OACnBH,EAAK1I,gBAAkBsH,EAAKwB,YAAYJ,CAC5C,CACAL,EAAAG,EAAAC,GAAA,IAAAM,EAAAP,EAAAlJ,UAgHC,OAhHDyJ,EASAC,QAAA,SAAQf,EAAQC,EAAaC,GAEzB,OADAM,EAAAnJ,UAAMyG,aAAYvG,KAACsF,KAAA,QAAS,IAAIiD,EAAeE,EAAQC,EAAaC,IAC7DrD,IACX,EACAiE,EAGAE,KAAA,WAGI,OAFAnE,KAAKoE,WAAa,UAClBpE,KAAKqE,SACErE,IACX,EACAiE,EAGAK,MAAA,WAKI,MAJwB,YAApBtE,KAAKoE,YAAgD,SAApBpE,KAAKoE,aACtCpE,KAAKuE,UACLvE,KAAKwE,WAEFxE,IACX,EACAiE,EAKAQ,KAAA,SAAKC,GACuB,SAApB1E,KAAKoE,YACLpE,KAAK2E,MAAMD,EAKnB,EACAT,EAKAW,OAAA,WACI5E,KAAKoE,WAAa,OAClBpE,KAAK6D,UAAW,EAChBF,EAAAnJ,UAAMyG,aAAYvG,UAAC,OACvB,EACAuJ,EAMAY,OAAA,SAAOxK,GACH,IAAM0D,EAAS1B,EAAahC,EAAM2F,KAAK+D,OAAOxH,YAC9CyD,KAAK8E,SAAS/G,EAClB,EACAkG,EAKAa,SAAA,SAAS/G,GACL4F,EAAAnJ,UAAMyG,aAAYvG,KAAAsF,KAAC,SAAUjC,EACjC,EACAkG,EAKAO,QAAA,SAAQO,GACJ/E,KAAKoE,WAAa,SAClBT,EAAAnJ,UAAMyG,aAAYvG,KAAAsF,KAAC,QAAS+E,EAChC,EACAd,EAKAe,MAAA,SAAMC,GAAS,EAAGhB,EAClBiB,UAAA,SAAUC,GAAoB,IAAZrB,EAAKvD,UAAA3D,OAAA,QAAAwI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,CAAA,EACtB,OAAQ4E,EACJ,MACAnF,KAAKqF,IACLrF,KAAKsF,IACLtF,KAAKwC,KAAK+C,KACVvF,KAAKwF,EAAO1B,IACnBG,EACDoB,EAAA,WACI,IAAMI,EAAWzF,KAAKwC,KAAKiD,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,KACrExB,EACDqB,EAAA,WACI,OAAItF,KAAKwC,KAAKmD,OACR3F,KAAKwC,KAAKoD,QAAUC,OAA0B,MAAnB7F,KAAKwC,KAAKmD,QACjC3F,KAAKwC,KAAKoD,QAAqC,KAA3BC,OAAO7F,KAAKwC,KAAKmD,OACpC,IAAM3F,KAAKwC,KAAKmD,KAGhB,IAEd1B,EACDuB,EAAA,SAAO1B,GACH,IAAMgC,EClIP,SAAgBhL,GACnB,IAAIiL,EAAM,GACV,IAAK,IAAI7J,KAAKpB,EACNA,EAAImH,eAAe/F,KACf6J,EAAInJ,SACJmJ,GAAO,KACXA,GAAOC,mBAAmB9J,GAAK,IAAM8J,mBAAmBlL,EAAIoB,KAGpE,OAAO6J,CACX,CDwH6B1H,CAAOyF,GAC5B,OAAOgC,EAAalJ,OAAS,IAAMkJ,EAAe,IACrDpC,CAAA,EAhI0BhE,GETlBuG,WAAOC,GAChB,SAAAD,IAAc,IAAA3C,EAEY,OADtBA,EAAA4C,EAAA5F,MAAAN,KAASO,YAAUP,MACdmG,GAAW,EAAM7C,CAC1B,CAACC,EAAA0C,EAAAC,GAAA,IAAAjC,EAAAgC,EAAAzL,UAwIA,OApIDyJ,EAMAI,OAAA,WACIrE,KAAKoG,GACT,EACAnC,EAMAe,MAAA,SAAMC,GAAS,IAAArB,EAAA5D,KACXA,KAAKoE,WAAa,UAClB,IAAMY,EAAQ,WACVpB,EAAKQ,WAAa,SAClBa,KAEJ,GAAIjF,KAAKmG,IAAanG,KAAK6D,SAAU,CACjC,IAAIwC,EAAQ,EACRrG,KAAKmG,IACLE,IACArG,KAAKI,KAAK,gBAAgB,aACpBiG,GAASrB,GACf,KAEChF,KAAK6D,WACNwC,IACArG,KAAKI,KAAK,SAAS,aACbiG,GAASrB,GACf,IAER,MAEIA,GAER,EACAf,EAKAmC,EAAA,WACIpG,KAAKmG,GAAW,EAChBnG,KAAKsG,SACLtG,KAAKiB,aAAa,OACtB,EACAgD,EAKAY,OAAA,SAAOxK,GAAM,IAAAkM,EAAAvG,MP/CK,SAACwG,EAAgBjK,GAGnC,IAFA,IAAMkK,EAAiBD,EAAe9K,MAAM+B,GACtCiH,EAAU,GACPxI,EAAI,EAAGA,EAAIuK,EAAe7J,OAAQV,IAAK,CAC5C,IAAMwK,EAAgBrK,EAAaoK,EAAevK,GAAIK,GAEtD,GADAmI,EAAQxE,KAAKwG,GACc,UAAvBA,EAActM,KACd,KAER,CACA,OAAOsK,CACX,EOmDQiC,CAActM,EAAM2F,KAAK+D,OAAOxH,YAAYvC,SAd3B,SAAC+D,GAMd,GAJI,YAAcwI,EAAKnC,YAA8B,SAAhBrG,EAAO3D,MACxCmM,EAAK3B,SAGL,UAAY7G,EAAO3D,KAEnB,OADAmM,EAAK/B,QAAQ,CAAEpB,YAAa,oCACrB,EAGXmD,EAAKzB,SAAS/G,MAKd,WAAaiC,KAAKoE,aAElBpE,KAAKmG,GAAW,EAChBnG,KAAKiB,aAAa,gBACd,SAAWjB,KAAKoE,YAChBpE,KAAKoG,IAKjB,EACAnC,EAKAM,QAAA,WAAU,IAAAqC,EAAA5G,KACAsE,EAAQ,WACVsC,EAAKjC,MAAM,CAAC,CAAEvK,KAAM,YAEpB,SAAW4F,KAAKoE,WAChBE,IAKAtE,KAAKI,KAAK,OAAQkE,EAE1B,EACAL,EAMAU,MAAA,SAAMD,GAAS,IAAAmC,EAAA7G,KACXA,KAAK6D,UAAW,EPnHF,SAACa,EAASvJ,GAE5B,IAAMyB,EAAS8H,EAAQ9H,OACjB6J,EAAiB,IAAIzF,MAAMpE,GAC7BkK,EAAQ,EACZpC,EAAQ1K,SAAQ,SAAC+D,EAAQ7B,GAErBlB,EAAa+C,GAAQ,GAAO,SAACzB,GACzBmK,EAAevK,GAAKI,IACdwK,IAAUlK,GACZzB,EAASsL,EAAeM,KAAKtJ,GAErC,GACJ,GACJ,COsGQuJ,CAActC,GAAS,SAACrK,GACpBwM,EAAKI,QAAQ5M,GAAM,WACfwM,EAAKhD,UAAW,EAChBgD,EAAK5F,aAAa,QACtB,GACJ,GACJ,EACAgD,EAKAiD,IAAA,WACI,IAAM/B,EAASnF,KAAKwC,KAAKoD,OAAS,QAAU,OACtC9B,EAAQ9D,KAAK8D,OAAS,GAQ5B,OANI,IAAU9D,KAAKwC,KAAK2E,oBACpBrD,EAAM9D,KAAKwC,KAAK4E,gBAAkBxE,KAEjC5C,KAAK9E,gBAAmB4I,EAAMuD,MAC/BvD,EAAMwD,IAAM,GAETtH,KAAKkF,UAAUC,EAAQrB,IACjCyD,EAAAtB,EAAA,CAAA,CAAAhM,IAAA,OAAAuN,IAvID,WACI,MAAO,SACX,IAAC,EAPwB9D,GCFzB+D,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,GAEH,CAEG,IAAMC,EAAUH,ECLvB,SAASI,IAAU,CACNC,IAAAA,WAAOC,GAOhB,SAAAD,EAAYtF,GAAM,IAAAc,EAEd,GADAA,EAAAyE,EAAArN,KAAAsF,KAAMwC,IAAKxC,KACa,oBAAbgI,SAA0B,CACjC,IAAMC,EAAQ,WAAaD,SAASE,SAChCvC,EAAOqC,SAASrC,KAEfA,IACDA,EAAOsC,EAAQ,MAAQ,MAE3B3E,EAAK6E,GACoB,oBAAbH,UACJxF,EAAKiD,WAAauC,SAASvC,UAC3BE,IAASnD,EAAKmD,IAC1B,CAAC,OAAArC,CACL,CACAC,EAAAuE,EAAAC,GAAA,IAAA9D,EAAA6D,EAAAtN,UA6BC,OA7BDyJ,EAOAgD,QAAA,SAAQ5M,EAAM0F,GAAI,IAAA6D,EAAA5D,KACRoI,EAAMpI,KAAKqI,QAAQ,CACrBC,OAAQ,OACRjO,KAAMA,IAEV+N,EAAIxI,GAAG,UAAWG,GAClBqI,EAAIxI,GAAG,SAAS,SAAC2I,EAAWlF,GACxBO,EAAKM,QAAQ,iBAAkBqE,EAAWlF,EAC9C,GACJ,EACAY,EAKAqC,OAAA,WAAS,IAAAC,EAAAvG,KACCoI,EAAMpI,KAAKqI,UACjBD,EAAIxI,GAAG,OAAQI,KAAK6E,OAAOnC,KAAK1C,OAChCoI,EAAIxI,GAAG,SAAS,SAAC2I,EAAWlF,GACxBkD,EAAKrC,QAAQ,iBAAkBqE,EAAWlF,EAC9C,IACArD,KAAKwI,QAAUJ,GAClBN,CAAA,EAnDwB7B,GAqDhBwC,WAAO9E,GAOhB,SAAA8E,EAAYC,EAAexB,EAAK1E,GAAM,IAAAoE,EAQnB,OAPfA,EAAAjD,EAAAjJ,YAAOsF,MACF0I,cAAgBA,EACrBnG,EAAqBqE,EAAOpE,GAC5BoE,EAAK+B,EAAQnG,EACboE,EAAKgC,EAAUpG,EAAK8F,QAAU,MAC9B1B,EAAKiC,EAAO3B,EACZN,EAAKkC,OAAQ1D,IAAc5C,EAAKnI,KAAOmI,EAAKnI,KAAO,KACnDuM,EAAKmC,IAAUnC,CACnB,CACArD,EAAAkF,EAAA9E,GAAA,IAAAqF,EAAAP,EAAAjO,UAgIC,OAhIDwO,EAKAD,EAAA,WAAU,IACFE,EADEpC,EAAA7G,KAEAwC,EAAOZ,EAAK5B,KAAK2I,EAAO,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aAClHnG,EAAK0G,UAAYlJ,KAAK2I,EAAMR,GAC5B,IAAMgB,EAAOnJ,KAAKoJ,EAAOpJ,KAAK0I,cAAclG,GAC5C,IACI2G,EAAIhF,KAAKnE,KAAK4I,EAAS5I,KAAK6I,GAAM,GAClC,IACI,GAAI7I,KAAK2I,EAAMU,aAGX,IAAK,IAAInN,KADTiN,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzCtJ,KAAK2I,EAAMU,aACjBrJ,KAAK2I,EAAMU,aAAapH,eAAe/F,IACvCiN,EAAII,iBAAiBrN,EAAG8D,KAAK2I,EAAMU,aAAanN,GAIhE,CACA,MAAOsN,GAAK,CACZ,GAAI,SAAWxJ,KAAK4I,EAChB,IACIO,EAAII,iBAAiB,eAAgB,2BACzC,CACA,MAAOC,GAAK,CAEhB,IACIL,EAAII,iBAAiB,SAAU,MACnC,CACA,MAAOC,GAAK,CACoB,QAA/BP,EAAKjJ,KAAK2I,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGS,WAAWP,GAE3E,oBAAqBA,IACrBA,EAAIQ,gBAAkB3J,KAAK2I,EAAMgB,iBAEjC3J,KAAK2I,EAAMiB,iBACXT,EAAIU,QAAU7J,KAAK2I,EAAMiB,gBAE7BT,EAAIW,mBAAqB,WACrB,IAAIb,EACmB,IAAnBE,EAAI/E,aAC4B,QAA/B6E,EAAKpC,EAAK8B,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGc,aAEpEZ,EAAIa,kBAAkB,gBAEtB,IAAMb,EAAI/E,aAEV,MAAQ+E,EAAIc,QAAU,OAASd,EAAIc,OACnCpD,EAAKqD,IAKLrD,EAAKtF,cAAa,WACdsF,EAAKsD,EAA+B,iBAAfhB,EAAIc,OAAsBd,EAAIc,OAAS,EAC/D,GAAE,KAGXd,EAAI1E,KAAKzE,KAAK8I,EACjB,CACD,MAAOU,GAOH,YAHAxJ,KAAKuB,cAAa,WACdsF,EAAKsD,EAASX,EACjB,GAAE,EAEP,CACwB,oBAAbY,WACPpK,KAAKqK,EAAS5B,EAAQ6B,gBACtB7B,EAAQ8B,SAASvK,KAAKqK,GAAUrK,KAExC,EACAgJ,EAKAmB,EAAA,SAASxC,GACL3H,KAAKiB,aAAa,QAAS0G,EAAK3H,KAAKoJ,GACrCpJ,KAAKwK,GAAS,EAClB,EACAxB,EAKAwB,EAAA,SAASC,GACL,QAAI,IAAuBzK,KAAKoJ,GAAQ,OAASpJ,KAAKoJ,EAAtD,CAIA,GADApJ,KAAKoJ,EAAKU,mBAAqBjC,EAC3B4C,EACA,IACIzK,KAAKoJ,EAAKsB,OACd,CACA,MAAOlB,GAAK,CAEQ,oBAAbY,iBACA3B,EAAQ8B,SAASvK,KAAKqK,GAEjCrK,KAAKoJ,EAAO,IAXZ,CAYJ,EACAJ,EAKAkB,EAAA,WACI,IAAM7P,EAAO2F,KAAKoJ,EAAKuB,aACV,OAATtQ,IACA2F,KAAKiB,aAAa,OAAQ5G,GAC1B2F,KAAKiB,aAAa,WAClBjB,KAAKwK,IAEb,EACAxB,EAKA0B,MAAA,WACI1K,KAAKwK,KACR/B,CAAA,EAjJwB/I,GA0J7B,GAPA+I,EAAQ6B,cAAgB,EACxB7B,EAAQ8B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArBhL,iBAAiC,CAE7CA,iBADyB,eAAgBsC,EAAa,WAAa,SAChC0I,GAAe,EACtD,CAEJ,SAASA,IACL,IAAK,IAAI3O,KAAKuM,EAAQ8B,SACd9B,EAAQ8B,SAAStI,eAAe/F,IAChCuM,EAAQ8B,SAASrO,GAAGwO,OAGhC,CACA,IACUvB,EADJ2B,GACI3B,EAAM4B,GAAW,CACnB7B,SAAS,MAEsB,OAArBC,EAAI6B,aASTC,YAAGC,GACZ,SAAAD,EAAYzI,GAAM,IAAA2I,EACdA,EAAAD,EAAAxQ,KAAAsF,KAAMwC,IAAKxC,KACX,IAAMgE,EAAcxB,GAAQA,EAAKwB,YACa,OAA9CmH,EAAKjQ,eAAiB4P,IAAY9G,EAAYmH,CAClD,CAIC,OAJA5H,EAAA0H,EAAAC,GAAAD,EAAAzQ,UACD6N,QAAA,WAAmB,IAAX7F,EAAIjC,UAAA3D,OAAA,QAAAwI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,CAAA,EAEX,OADA6K,EAAc5I,EAAM,CAAE2F,GAAInI,KAAKmI,IAAMnI,KAAKwC,MACnC,IAAIiG,EAAQsC,GAAY/K,KAAKkH,MAAO1E,IAC9CyI,CAAA,EAToBnD,GAWzB,SAASiD,GAAWvI,GAChB,IAAM0G,EAAU1G,EAAK0G,QAErB,IACI,GAAI,oBAAuBxB,kBAAoBwB,GAAWtB,GACtD,OAAO,IAAIF,cAEnB,CACA,MAAO8B,GAAK,CACZ,IAAKN,EACD,IACI,OAAO,IAAI/G,EAAW,CAAC,UAAUkJ,OAAO,UAAUtE,KAAK,OAAM,oBACjE,CACA,MAAOyC,GAAK,CAEpB,CCzQA,IAAM8B,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,YAAMxF,GAAA,SAAAwF,IAAA,OAAAxF,EAAA5F,MAAAN,KAAAO,YAAAP,IAAA,CAAAuD,EAAAmI,EAAAxF,GAAA,IAAAjC,EAAAyH,EAAAlR,UA6Fd,OA7FcyJ,EAIfI,OAAA,WACI,IAAM6C,EAAMlH,KAAKkH,MACXyE,EAAY3L,KAAKwC,KAAKmJ,UAEtBnJ,EAAO8I,GACP,CAAA,EACA1J,EAAK5B,KAAKwC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMxC,KAAKwC,KAAK6G,eACV7G,EAAKoJ,QAAU5L,KAAKwC,KAAK6G,cAE7B,IACIrJ,KAAK6L,GAAK7L,KAAK8L,aAAa5E,EAAKyE,EAAWnJ,EAC/C,CACD,MAAOmF,GACH,OAAO3H,KAAKiB,aAAa,QAAS0G,EACtC,CACA3H,KAAK6L,GAAGtP,WAAayD,KAAK+D,OAAOxH,WACjCyD,KAAK+L,mBACT,EACA9H,EAKA8H,kBAAA,WAAoB,IAAAzI,EAAAtD,KAChBA,KAAK6L,GAAGG,OAAS,WACT1I,EAAKd,KAAKyJ,WACV3I,EAAKuI,GAAGK,EAAQC,QAEpB7I,EAAKsB,UAET5E,KAAK6L,GAAGO,QAAU,SAACC,GAAU,OAAK/I,EAAKkB,QAAQ,CAC3CpB,YAAa,8BACbC,QAASgJ,GACX,EACFrM,KAAK6L,GAAGS,UAAY,SAACC,GAAE,OAAKjJ,EAAKuB,OAAO0H,EAAGlS,KAAK,EAChD2F,KAAK6L,GAAGW,QAAU,SAAChD,GAAC,OAAKlG,EAAKY,QAAQ,kBAAmBsF,EAAE,GAC9DvF,EACDU,MAAA,SAAMD,GAAS,IAAAd,EAAA5D,KACXA,KAAK6D,UAAW,EAGhB,IADA,IAAA4I,EAAAA,WAEI,IAAM1O,EAAS2G,EAAQxI,GACjBwQ,EAAaxQ,IAAMwI,EAAQ9H,OAAS,EAC1C5B,EAAa+C,EAAQ6F,EAAK1I,gBAAgB,SAACb,GAIvC,IACIuJ,EAAKqD,QAAQlJ,EAAQ1D,EACzB,CACA,MAAOmP,GACP,CACIkD,GAGAtL,GAAS,WACLwC,EAAKC,UAAW,EAChBD,EAAK3C,aAAa,QACtB,GAAG2C,EAAKrC,aAEhB,KApBKrF,EAAI,EAAGA,EAAIwI,EAAQ9H,OAAQV,IAAGuQ,KAsB1CxI,EACDM,QAAA,gBAC2B,IAAZvE,KAAK6L,KACZ7L,KAAK6L,GAAGW,QAAU,aAClBxM,KAAK6L,GAAGvH,QACRtE,KAAK6L,GAAK,KAElB,EACA5H,EAKAiD,IAAA,WACI,IAAM/B,EAASnF,KAAKwC,KAAKoD,OAAS,MAAQ,KACpC9B,EAAQ9D,KAAK8D,OAAS,GAS5B,OAPI9D,KAAKwC,KAAK2E,oBACVrD,EAAM9D,KAAKwC,KAAK4E,gBAAkBxE,KAGjC5C,KAAK9E,iBACN4I,EAAMwD,IAAM,GAETtH,KAAKkF,UAAUC,EAAQrB,IACjCyD,EAAAmE,EAAA,CAAA,CAAAzR,IAAA,OAAAuN,IA5FD,WACI,MAAO,WACX,IAAC,EAHuB9D,GA+FtBiJ,GAAgBxK,EAAWyK,WAAazK,EAAW0K,aAU5CC,YAAEC,GAAA,SAAAD,IAAA,OAAAC,EAAAzM,MAAAN,KAAAO,YAAAP,IAAA,CAAAuD,EAAAuJ,EAAAC,GAAA,IAAA/D,EAAA8D,EAAAtS,UAUV,OAVUwO,EACX8C,aAAA,SAAa5E,EAAKyE,EAAWnJ,GACzB,OAAQ8I,GAIF,IAAIqB,GAAczF,EAAKyE,EAAWnJ,GAHlCmJ,EACI,IAAIgB,GAAczF,EAAKyE,GACvB,IAAIgB,GAAczF,IAE/B8B,EACD/B,QAAA,SAAQ+F,EAAS3S,GACb2F,KAAK6L,GAAGpH,KAAKpK,IAChByS,CAAA,EAVmBpB,ICtGXuB,YAAE/G,GAAA,SAAA+G,IAAA,OAAA/G,EAAA5F,MAAAN,KAAAO,YAAAP,IAAA,CAAAuD,EAAA0J,EAAA/G,GAAA,IAAAjC,EAAAgJ,EAAAzS,UAmEV,OAnEUyJ,EAIXI,OAAA,WAAS,IAAAf,EAAAtD,KACL,IAEIA,KAAKkN,EAAa,IAAIC,aAAanN,KAAKkF,UAAU,SAAUlF,KAAKwC,KAAK4K,iBAAiBpN,KAAKqN,MAC/F,CACD,MAAO1F,GACH,OAAO3H,KAAKiB,aAAa,QAAS0G,EACtC,CACA3H,KAAKkN,EAAWI,OACXpP,MAAK,WACNoF,EAAKkB,SACT,IAAE,OACS,SAACmD,GACRrE,EAAKY,QAAQ,qBAAsByD,EACvC,IAEA3H,KAAKkN,EAAWK,MAAMrP,MAAK,WACvBoF,EAAK4J,EAAWM,4BAA4BtP,MAAK,SAACuP,GAC9C,IAAMC,EXqDf,SAAmCC,EAAYpR,GAC7CH,IACDA,EAAe,IAAIwR,aAEvB,IAAM3O,EAAS,GACX4O,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAIlQ,gBAAgB,CACvBC,UAASA,SAACsB,EAAOpB,GAEb,IADAiB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVyO,EAAqC,CACrC,GAAI7O,EAAYC,GAAU,EACtB,MAEJ,IAAMV,EAASc,EAAaJ,EAAQ,GACpC8O,IAAkC,KAAtBxP,EAAO,IACnBuP,EAA6B,IAAZvP,EAAO,GAEpBsP,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEhB,MACK,GAAc,IAAVD,EAAiD,CACtD,GAAI7O,EAAYC,GAAU,EACtB,MAEJ,IAAM+O,EAAc3O,EAAaJ,EAAQ,GACzC6O,EAAiB,IAAIrP,SAASuP,EAAYjT,OAAQiT,EAAYlS,WAAYkS,EAAYpR,QAAQqR,UAAU,GACxGJ,EAAQ,CACZ,MACK,GAAc,IAAVA,EAAiD,CACtD,GAAI7O,EAAYC,GAAU,EACtB,MAEJ,IAAM+O,EAAc3O,EAAaJ,EAAQ,GACnCN,EAAO,IAAIF,SAASuP,EAAYjT,OAAQiT,EAAYlS,WAAYkS,EAAYpR,QAC5EsR,EAAIvP,EAAKwP,UAAU,GACzB,GAAID,EAAInL,KAAKqL,IAAI,EAAG,IAAW,EAAG,CAE9BpQ,EAAWe,QAAQ5E,GACnB,KACJ,CACA2T,EAAiBI,EAAInL,KAAKqL,IAAI,EAAG,IAAMzP,EAAKwP,UAAU,GACtDN,EAAQ,CACZ,KACK,CACD,GAAI7O,EAAYC,GAAU6O,EACtB,MAEJ,IAAMzT,EAAOgF,EAAaJ,EAAQ6O,GAClC9P,EAAWe,QAAQ1C,EAAa0R,EAAW1T,EAAO+B,EAAaoB,OAAOnD,GAAOkC,IAC7EsR,EAAQ,CACZ,CACA,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrD3P,EAAWe,QAAQ5E,GACnB,KACJ,CACJ,CACJ,GAER,CWxHsCkU,CAA0BxI,OAAOyI,iBAAkBhL,EAAKS,OAAOxH,YAC/EgS,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgB/Q,IACtB+Q,EAAcH,SAASI,OAAOnB,EAAO5J,UACrCP,EAAKuL,EAAUF,EAAc9K,SAASiL,aACzB,SAAPC,IACFR,EACKQ,OACA7Q,MAAK,SAAAjD,GAAqB,IAAlB+T,EAAI/T,EAAJ+T,KAAMvH,EAAKxM,EAALwM,MACXuH,IAGJ1L,EAAKwB,SAAS2C,GACdsH,IACH,WACU,SAACpH,GACX,IAELoH,GACA,IAAMhR,EAAS,CAAE3D,KAAM,QACnBkJ,EAAKQ,MAAMuD,MACXtJ,EAAO1D,KAAI,WAAAgR,OAAc/H,EAAKQ,MAAMuD,IAAO,OAE/C/D,EAAKuL,EAAQlK,MAAM5G,GAAQG,MAAK,WAAA,OAAMoF,EAAKsB,WAC/C,GACJ,KACHX,EACDU,MAAA,SAAMD,GAAS,IAAAd,EAAA5D,KACXA,KAAK6D,UAAW,EAChB,IADsB,IAAA4I,EAAAA,WAElB,IAAM1O,EAAS2G,EAAQxI,GACjBwQ,EAAaxQ,IAAMwI,EAAQ9H,OAAS,EAC1CgH,EAAKiL,EAAQlK,MAAM5G,GAAQG,MAAK,WACxBwO,GACAtL,GAAS,WACLwC,EAAKC,UAAW,EAChBD,EAAK3C,aAAa,QACtB,GAAG2C,EAAKrC,aAEhB,KAVKrF,EAAI,EAAGA,EAAIwI,EAAQ9H,OAAQV,IAAGuQ,KAY1CxI,EACDM,QAAA,WACI,IAAI0E,EACuB,QAA1BA,EAAKjJ,KAAKkN,SAA+B,IAAPjE,GAAyBA,EAAG3E,SAClEiD,EAAA0F,EAAA,CAAA,CAAAhT,IAAA,OAAAuN,IAlED,WACI,MAAO,cACX,IAAC,EAHmB9D,GCRXuL,GAAa,CACtBC,UAAWpC,GACXqC,aAAclC,GACdmC,QAASnE,ICaPoE,GAAK,sPACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAMxJ,GAClB,GAAIA,EAAInJ,OAAS,IACb,KAAM,eAEV,IAAM4S,EAAMzJ,EAAK0J,EAAI1J,EAAIL,QAAQ,KAAM8D,EAAIzD,EAAIL,QAAQ,MAC7C,GAAN+J,IAAiB,GAANjG,IACXzD,EAAMA,EAAIpJ,UAAU,EAAG8S,GAAK1J,EAAIpJ,UAAU8S,EAAGjG,GAAGkG,QAAQ,KAAM,KAAO3J,EAAIpJ,UAAU6M,EAAGzD,EAAInJ,SAG9F,IADA,IAwBmBkH,EACbzJ,EAzBFsV,EAAIN,GAAGO,KAAK7J,GAAO,IAAKmB,EAAM,CAAE,EAAEhL,EAAI,GACnCA,KACHgL,EAAIoI,GAAMpT,IAAMyT,EAAEzT,IAAM,GAU5B,OARU,GAANuT,IAAiB,GAANjG,IACXtC,EAAI2I,OAASL,EACbtI,EAAI4I,KAAO5I,EAAI4I,KAAKnT,UAAU,EAAGuK,EAAI4I,KAAKlT,OAAS,GAAG8S,QAAQ,KAAM,KACpExI,EAAI6I,UAAY7I,EAAI6I,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9ExI,EAAI8I,SAAU,GAElB9I,EAAI+I,UAIR,SAAmBnV,EAAKyK,GACpB,IAAM2K,EAAO,WAAYC,EAAQ5K,EAAKmK,QAAQQ,EAAM,KAAKxU,MAAM,KACvC,KAApB6J,EAAK9F,MAAM,EAAG,IAA6B,IAAhB8F,EAAK3I,QAChCuT,EAAMtP,OAAO,EAAG,GAEE,KAAlB0E,EAAK9F,OAAO,IACZ0Q,EAAMtP,OAAOsP,EAAMvT,OAAS,EAAG,GAEnC,OAAOuT,CACX,CAboBF,CAAU/I,EAAKA,EAAU,MACzCA,EAAIkJ,UAaetM,EAbUoD,EAAW,MAclC7M,EAAO,CAAA,EACbyJ,EAAM4L,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAjW,EAAKiW,GAAMC,EAEnB,IACOlW,GAnBA6M,CACX,CCrCA,IAAMsJ,GAAiD,mBAArB3Q,kBACC,mBAAxBa,oBACL+P,GAA0B,GAC5BD,IAGA3Q,iBAAiB,WAAW,WACxB4Q,GAAwBzW,SAAQ,SAAC0W,GAAQ,OAAKA,MACjD,IAAE,GAyBMC,IAAAA,YAAoBhN,GAO7B,SAAAgN,EAAYzJ,EAAK1E,GAAM,IAAAc,EAiBnB,IAhBAA,EAAAK,EAAAjJ,YAAOsF,MACFzD,WX7BoB,cW8BzB+G,EAAKsN,YAAc,GACnBtN,EAAKuN,EAAiB,EACtBvN,EAAKwN,GAAiB,EACtBxN,EAAKyN,GAAgB,EACrBzN,EAAK0N,GAAe,EAKpB1N,EAAK2N,EAAmBC,IACpBhK,GAAO,WAAQiK,EAAYjK,KAC3B1E,EAAO0E,EACPA,EAAM,MAENA,EAAK,CACL,IAAMkK,EAAY7B,GAAMrI,GACxB1E,EAAKiD,SAAW2L,EAAUtB,KAC1BtN,EAAKoD,OACsB,UAAvBwL,EAAUlJ,UAA+C,QAAvBkJ,EAAUlJ,SAChD1F,EAAKmD,KAAOyL,EAAUzL,KAClByL,EAAUtN,QACVtB,EAAKsB,MAAQsN,EAAUtN,MAC/B,MACStB,EAAKsN,OACVtN,EAAKiD,SAAW8J,GAAM/M,EAAKsN,MAAMA,MA2ExB,OAzEbvN,EAAqBe,EAAOd,GAC5Bc,EAAKsC,OACD,MAAQpD,EAAKoD,OACPpD,EAAKoD,OACe,oBAAboC,UAA4B,WAAaA,SAASE,SAC/D1F,EAAKiD,WAAajD,EAAKmD,OAEvBnD,EAAKmD,KAAOrC,EAAKsC,OAAS,MAAQ,MAEtCtC,EAAKmC,SACDjD,EAAKiD,WACoB,oBAAbuC,SAA2BA,SAASvC,SAAW,aAC/DnC,EAAKqC,KACDnD,EAAKmD,OACoB,oBAAbqC,UAA4BA,SAASrC,KACvCqC,SAASrC,KACTrC,EAAKsC,OACD,MACA,MAClBtC,EAAK2L,WAAa,GAClB3L,EAAK+N,EAAoB,GACzB7O,EAAKyM,WAAWjV,SAAQ,SAACsX,GACrB,IAAMC,EAAgBD,EAAE9W,UAAU6S,KAClC/J,EAAK2L,WAAW/O,KAAKqR,GACrBjO,EAAK+N,EAAkBE,GAAiBD,CAC5C,IACAhO,EAAKd,KAAO4I,EAAc,CACtB7F,KAAM,aACNiM,OAAO,EACP7H,iBAAiB,EACjB8H,SAAS,EACTrK,eAAgB,IAChBsK,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEf1E,iBAAkB,CAAE,EACpB2E,qBAAqB,GACtBvP,GACHc,EAAKd,KAAK+C,KACNjC,EAAKd,KAAK+C,KAAKmK,QAAQ,MAAO,KACzBpM,EAAKd,KAAKmP,iBAAmB,IAAM,IACb,iBAApBrO,EAAKd,KAAKsB,QACjBR,EAAKd,KAAKsB,MRhGf,SAAgBkO,GAGnB,IAFA,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAGtW,MAAM,KACZQ,EAAI,EAAGiW,EAAID,EAAMtV,OAAQV,EAAIiW,EAAGjW,IAAK,CAC1C,IAAIkW,EAAOF,EAAMhW,GAAGR,MAAM,KAC1BuW,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC/D,CACA,OAAOH,CACX,CQwF8BzU,CAAO8F,EAAKd,KAAKsB,QAEnC0M,KACIlN,EAAKd,KAAKuP,sBAIVzO,EAAKgP,EAA6B,WAC1BhP,EAAKiP,YAELjP,EAAKiP,UAAU9R,qBACf6C,EAAKiP,UAAUjO,UAGvBzE,iBAAiB,eAAgByD,EAAKgP,GAA4B,IAEhD,cAAlBhP,EAAKmC,WACLnC,EAAKkP,EAAwB,WACzBlP,EAAKmP,EAAS,kBAAmB,CAC7BrP,YAAa,6BAGrBqN,GAAwBvQ,KAAKoD,EAAKkP,KAGtClP,EAAKd,KAAKmH,kBACVrG,EAAKoP,OAAaC,GAEtBrP,EAAKsP,IAAQtP,CACjB,CACAC,EAAAoN,EAAAhN,GAAA,IAAAM,EAAA0M,EAAAnW,UAiYC,OAjYDyJ,EAOA4O,gBAAA,SAAgBxF,GACZ,IAAMvJ,EAAQsH,EAAc,CAAA,EAAIpL,KAAKwC,KAAKsB,OAE1CA,EAAMgP,IdPU,EcShBhP,EAAMyO,UAAYlF,EAEdrN,KAAK+S,KACLjP,EAAMuD,IAAMrH,KAAK+S,IACrB,IAAMvQ,EAAO4I,EAAc,GAAIpL,KAAKwC,KAAM,CACtCsB,MAAAA,EACAC,OAAQ/D,KACRyF,SAAUzF,KAAKyF,SACfG,OAAQ5F,KAAK4F,OACbD,KAAM3F,KAAK2F,MACZ3F,KAAKwC,KAAK4K,iBAAiBC,IAC9B,OAAO,IAAIrN,KAAKqR,EAAkBhE,GAAM7K,EAC5C,EACAyB,EAKA2O,EAAA,WAAQ,IAAAhP,EAAA5D,KACJ,GAA+B,IAA3BA,KAAKiP,WAAWrS,OAApB,CAOA,IAAM2U,EAAgBvR,KAAKwC,KAAKkP,iBAC5Bf,EAAqBqC,wBACqB,IAA1ChT,KAAKiP,WAAWvJ,QAAQ,aACtB,YACA1F,KAAKiP,WAAW,GACtBjP,KAAKoE,WAAa,UAClB,IAAMmO,EAAYvS,KAAK6S,gBAAgBtB,GACvCgB,EAAUpO,OACVnE,KAAKiT,aAAaV,EATlB,MAJIvS,KAAKuB,cAAa,WACdqC,EAAK3C,aAAa,QAAS,0BAC9B,GAAE,EAYX,EACAgD,EAKAgP,aAAA,SAAaV,GAAW,IAAAhM,EAAAvG,KAChBA,KAAKuS,WACLvS,KAAKuS,UAAU9R,qBAGnBT,KAAKuS,UAAYA,EAEjBA,EACK3S,GAAG,QAASI,KAAKkT,EAASxQ,KAAK1C,OAC/BJ,GAAG,SAAUI,KAAKmT,EAAUzQ,KAAK1C,OACjCJ,GAAG,QAASI,KAAKmK,EAASzH,KAAK1C,OAC/BJ,GAAG,SAAS,SAACuD,GAAM,OAAKoD,EAAKkM,EAAS,kBAAmBtP,KAClE,EACAc,EAKAW,OAAA,WACI5E,KAAKoE,WAAa,OAClBuM,EAAqBqC,sBACjB,cAAgBhT,KAAKuS,UAAUlF,KACnCrN,KAAKiB,aAAa,QAClBjB,KAAKoT,OACT,EACAnP,EAKAkP,EAAA,SAAUpV,GACN,GAAI,YAAciC,KAAKoE,YACnB,SAAWpE,KAAKoE,YAChB,YAAcpE,KAAKoE,WAInB,OAHApE,KAAKiB,aAAa,SAAUlD,GAE5BiC,KAAKiB,aAAa,aACVlD,EAAO3D,MACX,IAAK,OACD4F,KAAKqT,YAAYC,KAAK/D,MAAMxR,EAAO1D,OACnC,MACJ,IAAK,OACD2F,KAAKuT,EAAY,QACjBvT,KAAKiB,aAAa,QAClBjB,KAAKiB,aAAa,QAClBjB,KAAKwT,IACL,MACJ,IAAK,QACD,IAAM7L,EAAM,IAAIlE,MAAM,gBAEtBkE,EAAI8L,KAAO1V,EAAO1D,KAClB2F,KAAKmK,EAASxC,GACd,MACJ,IAAK,UACD3H,KAAKiB,aAAa,OAAQlD,EAAO1D,MACjC2F,KAAKiB,aAAa,UAAWlD,EAAO1D,MAMpD,EACA4J,EAMAoP,YAAA,SAAYhZ,GACR2F,KAAKiB,aAAa,YAAa5G,GAC/B2F,KAAK+S,GAAK1Y,EAAKgN,IACfrH,KAAKuS,UAAUzO,MAAMuD,IAAMhN,EAAKgN,IAChCrH,KAAK8Q,EAAgBzW,EAAKqZ,aAC1B1T,KAAK+Q,EAAe1W,EAAKsZ,YACzB3T,KAAKgR,EAAc3W,EAAKsT,WACxB3N,KAAK4E,SAED,WAAa5E,KAAKoE,YAEtBpE,KAAKwT,GACT,EACAvP,EAKAuP,EAAA,WAAoB,IAAA5M,EAAA5G,KAChBA,KAAK2C,eAAe3C,KAAK4T,GACzB,IAAMC,EAAQ7T,KAAK8Q,EAAgB9Q,KAAK+Q,EACxC/Q,KAAKiR,EAAmBpO,KAAKC,MAAQ+Q,EACrC7T,KAAK4T,EAAoB5T,KAAKuB,cAAa,WACvCqF,EAAK6L,EAAS,eACjB,GAAEoB,GACC7T,KAAKwC,KAAKyJ,WACVjM,KAAK4T,EAAkBzH,OAE/B,EACAlI,EAKAiP,EAAA,WACIlT,KAAK4Q,YAAY/P,OAAO,EAAGb,KAAK6Q,GAIhC7Q,KAAK6Q,EAAiB,EAClB,IAAM7Q,KAAK4Q,YAAYhU,OACvBoD,KAAKiB,aAAa,SAGlBjB,KAAKoT,OAEb,EACAnP,EAKAmP,MAAA,WACI,GAAI,WAAapT,KAAKoE,YAClBpE,KAAKuS,UAAU1O,WACd7D,KAAK8T,WACN9T,KAAK4Q,YAAYhU,OAAQ,CACzB,IAAM8H,EAAU1E,KAAK+T,IACrB/T,KAAKuS,UAAU9N,KAAKC,GAGpB1E,KAAK6Q,EAAiBnM,EAAQ9H,OAC9BoD,KAAKiB,aAAa,QACtB,CACJ,EACAgD,EAMA8P,EAAA,WAII,KAH+B/T,KAAKgR,GACR,YAAxBhR,KAAKuS,UAAUlF,MACfrN,KAAK4Q,YAAYhU,OAAS,GAE1B,OAAOoD,KAAK4Q,YAGhB,IADA,IVrUmB9V,EUqUfkZ,EAAc,EACT9X,EAAI,EAAGA,EAAI8D,KAAK4Q,YAAYhU,OAAQV,IAAK,CAC9C,IAAM7B,EAAO2F,KAAK4Q,YAAY1U,GAAG7B,KAIjC,GAHIA,IACA2Z,GVxUO,iBADIlZ,EUyUeT,GVlU1C,SAAoB0L,GAEhB,IADA,IAAIkO,EAAI,EAAGrX,EAAS,EACXV,EAAI,EAAGiW,EAAIpM,EAAInJ,OAAQV,EAAIiW,EAAGjW,KACnC+X,EAAIlO,EAAI5J,WAAWD,IACX,IACJU,GAAU,EAELqX,EAAI,KACTrX,GAAU,EAELqX,EAAI,OAAUA,GAAK,MACxBrX,GAAU,GAGVV,IACAU,GAAU,GAGlB,OAAOA,CACX,CAxBesX,CAAWpZ,GAGfiI,KAAKoR,KAPQ,MAOFrZ,EAAIiB,YAAcjB,EAAIwE,QUsU5BpD,EAAI,GAAK8X,EAAchU,KAAKgR,EAC5B,OAAOhR,KAAK4Q,YAAYnR,MAAM,EAAGvD,GAErC8X,GAAe,CACnB,CACA,OAAOhU,KAAK4Q,WAChB,EAUA3M,EAAcmQ,EAAA,WAAkB,IAAAvN,EAAA7G,KAC5B,IAAKA,KAAKiR,EACN,OAAO,EACX,IAAMoD,EAAaxR,KAAKC,MAAQ9C,KAAKiR,EAOrC,OANIoD,IACArU,KAAKiR,EAAmB,EACxB7P,GAAS,WACLyF,EAAK4L,EAAS,eAClB,GAAGzS,KAAKuB,eAEL8S,CACX,EACApQ,EAQAU,MAAA,SAAM2P,EAAKC,EAASxU,GAEhB,OADAC,KAAKuT,EAAY,UAAWe,EAAKC,EAASxU,GACnCC,IACX,EACAiE,EAQAQ,KAAA,SAAK6P,EAAKC,EAASxU,GAEf,OADAC,KAAKuT,EAAY,UAAWe,EAAKC,EAASxU,GACnCC,IACX,EACAiE,EASAsP,EAAA,SAAYnZ,EAAMC,EAAMka,EAASxU,GAS7B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAO+K,GAEP,mBAAsBmP,IACtBxU,EAAKwU,EACLA,EAAU,MAEV,YAAcvU,KAAKoE,YAAc,WAAapE,KAAKoE,WAAvD,EAGAmQ,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,IAAMzW,EAAS,CACX3D,KAAMA,EACNC,KAAMA,EACNka,QAASA,GAEbvU,KAAKiB,aAAa,eAAgBlD,GAClCiC,KAAK4Q,YAAY1Q,KAAKnC,GAClBgC,GACAC,KAAKI,KAAK,QAASL,GACvBC,KAAKoT,OAZL,CAaJ,EACAnP,EAGAK,MAAA,WAAQ,IAAA6G,EAAAnL,KACEsE,EAAQ,WACV6G,EAAKsH,EAAS,gBACdtH,EAAKoH,UAAUjO,SAEbmQ,EAAkB,SAAlBA,IACFtJ,EAAK9K,IAAI,UAAWoU,GACpBtJ,EAAK9K,IAAI,eAAgBoU,GACzBnQ,KAEEoQ,EAAiB,WAEnBvJ,EAAK/K,KAAK,UAAWqU,GACrBtJ,EAAK/K,KAAK,eAAgBqU,IAqB9B,MAnBI,YAAczU,KAAKoE,YAAc,SAAWpE,KAAKoE,aACjDpE,KAAKoE,WAAa,UACdpE,KAAK4Q,YAAYhU,OACjBoD,KAAKI,KAAK,SAAS,WACX+K,EAAK2I,UACLY,IAGApQ,GAER,IAEKtE,KAAK8T,UACVY,IAGApQ,KAGDtE,IACX,EACAiE,EAKAkG,EAAA,SAASxC,GAEL,GADAgJ,EAAqBqC,uBAAwB,EACzChT,KAAKwC,KAAKmS,kBACV3U,KAAKiP,WAAWrS,OAAS,GACL,YAApBoD,KAAKoE,WAEL,OADApE,KAAKiP,WAAW1P,QACTS,KAAK4S,IAEhB5S,KAAKiB,aAAa,QAAS0G,GAC3B3H,KAAKyS,EAAS,kBAAmB9K,EACrC,EACA1D,EAKAwO,EAAA,SAAStP,EAAQC,GACb,GAAI,YAAcpD,KAAKoE,YACnB,SAAWpE,KAAKoE,YAChB,YAAcpE,KAAKoE,WAAY,CAS/B,GAPApE,KAAK2C,eAAe3C,KAAK4T,GAEzB5T,KAAKuS,UAAU9R,mBAAmB,SAElCT,KAAKuS,UAAUjO,QAEftE,KAAKuS,UAAU9R,qBACX+P,KACIxQ,KAAKsS,GACL5R,oBAAoB,eAAgBV,KAAKsS,GAA4B,GAErEtS,KAAKwS,GAAuB,CAC5B,IAAMtW,EAAIuU,GAAwB/K,QAAQ1F,KAAKwS,IACpC,IAAPtW,GACAuU,GAAwB5P,OAAO3E,EAAG,EAE1C,CAGJ8D,KAAKoE,WAAa,SAElBpE,KAAK+S,GAAK,KAEV/S,KAAKiB,aAAa,QAASkC,EAAQC,GAGnCpD,KAAK4Q,YAAc,GACnB5Q,KAAK6Q,EAAiB,CAC1B,GACHF,CAAA,EAhfqCjR,GAkf1CiR,GAAqBzI,SdhYG,EcwZX0M,IAAAA,YAAiBC,GAC1B,SAAAD,IAAc,IAAAE,EAEU,OADpBA,EAAAD,EAAAvU,MAAAN,KAASO,YAAUP,MACd+U,EAAY,GAAGD,CACxB,CAACvR,EAAAqR,EAAAC,GAAA,IAAA7L,EAAA4L,EAAApa,UAgIA,OAhIAwO,EACDpE,OAAA,WAEI,GADAiQ,EAAAra,UAAMoK,OAAMlK,KAAAsF,MACR,SAAWA,KAAKoE,YAAcpE,KAAKwC,KAAKiP,QACxC,IAAK,IAAIvV,EAAI,EAAGA,EAAI8D,KAAK+U,EAAUnY,OAAQV,IACvC8D,KAAKgV,GAAOhV,KAAK+U,EAAU7Y,GAGvC,EACA8M,EAMAgM,GAAA,SAAO3H,GAAM,IAAA4H,EAAAjV,KACLuS,EAAYvS,KAAK6S,gBAAgBxF,GACjC6H,GAAS,EACbvE,GAAqBqC,uBAAwB,EAC7C,IAAMmC,EAAkB,WAChBD,IAEJ3C,EAAU9N,KAAK,CAAC,CAAErK,KAAM,OAAQC,KAAM,WACtCkY,EAAUnS,KAAK,UAAU,SAACkU,GACtB,IAAIY,EAEJ,GAAI,SAAWZ,EAAIla,MAAQ,UAAYka,EAAIja,KAAM,CAG7C,GAFA4a,EAAKnB,WAAY,EACjBmB,EAAKhU,aAAa,YAAasR,IAC1BA,EACD,OACJ5B,GAAqBqC,sBACjB,cAAgBT,EAAUlF,KAC9B4H,EAAK1C,UAAUvN,OAAM,WACbkQ,GAEA,WAAaD,EAAK7Q,aAEtBgR,IACAH,EAAKhC,aAAaV,GAClBA,EAAU9N,KAAK,CAAC,CAAErK,KAAM,aACxB6a,EAAKhU,aAAa,UAAWsR,GAC7BA,EAAY,KACZ0C,EAAKnB,WAAY,EACjBmB,EAAK7B,QACT,GACJ,KACK,CACD,IAAMzL,EAAM,IAAIlE,MAAM,eAEtBkE,EAAI4K,UAAYA,EAAUlF,KAC1B4H,EAAKhU,aAAa,eAAgB0G,EACtC,CACJ,MAEJ,SAAS0N,IACDH,IAGJA,GAAS,EACTE,IACA7C,EAAUjO,QACViO,EAAY,KAChB,CAEA,IAAM/F,EAAU,SAAC7E,GACb,IAAM2N,EAAQ,IAAI7R,MAAM,gBAAkBkE,GAE1C2N,EAAM/C,UAAYA,EAAUlF,KAC5BgI,IACAJ,EAAKhU,aAAa,eAAgBqU,IAEtC,SAASC,IACL/I,EAAQ,mBACZ,CAEA,SAASJ,IACLI,EAAQ,gBACZ,CAEA,SAASgJ,EAAUC,GACXlD,GAAakD,EAAGpI,OAASkF,EAAUlF,MACnCgI,GAER,CAEA,IAAMD,EAAU,WACZ7C,EAAU/R,eAAe,OAAQ2U,GACjC5C,EAAU/R,eAAe,QAASgM,GAClC+F,EAAU/R,eAAe,QAAS+U,GAClCN,EAAK5U,IAAI,QAAS+L,GAClB6I,EAAK5U,IAAI,YAAamV,IAE1BjD,EAAUnS,KAAK,OAAQ+U,GACvB5C,EAAUnS,KAAK,QAASoM,GACxB+F,EAAUnS,KAAK,QAASmV,GACxBvV,KAAKI,KAAK,QAASgM,GACnBpM,KAAKI,KAAK,YAAaoV,IACyB,IAA5CxV,KAAK+U,EAAUrP,QAAQ,iBACd,iBAAT2H,EAEArN,KAAKuB,cAAa,WACT2T,GACD3C,EAAUpO,MAEjB,GAAE,KAGHoO,EAAUpO,QAEjB6E,EACDqK,YAAA,SAAYhZ,GACR2F,KAAK+U,EAAY/U,KAAK0V,GAAgBrb,EAAKsb,UAC3Cd,EAAAra,UAAM6Y,YAAW3Y,UAACL,EACtB,EACA2O,EAMA0M,GAAA,SAAgBC,GAEZ,IADA,IAAMC,EAAmB,GAChB1Z,EAAI,EAAGA,EAAIyZ,EAAS/Y,OAAQV,KAC5B8D,KAAKiP,WAAWvJ,QAAQiQ,EAASzZ,KAClC0Z,EAAiB1V,KAAKyV,EAASzZ,IAEvC,OAAO0Z,GACVhB,CAAA,EApIkCjE,IAyJ1BkF,YAAMC,GACf,SAAAD,EAAY3O,GAAgB,IAAX1E,EAAIjC,UAAA3D,OAAA,QAAAwI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,CAAA,EACdwV,EAAmB,WAAf5E,EAAOjK,GAAmBA,EAAM1E,EAMzC,QALIuT,EAAE9G,YACF8G,EAAE9G,YAAyC,iBAApB8G,EAAE9G,WAAW,MACrC8G,EAAE9G,YAAc8G,EAAE9G,YAAc,CAAC,UAAW,YAAa,iBACpD+G,KAAI,SAACzE,GAAa,OAAK0E,GAAmB1E,EAAc,IACxD2E,QAAO,SAAC5E,GAAC,QAAOA,MAEzBwE,EAAApb,UAAMwM,EAAK6O,IAAE/V,IACjB,CAAC,OAAAuD,EAAAsS,EAAAC,GAAAD,CAAA,EAVuBjB,ICxsBJiB,GAAO3N,yBCD/B,SAASiO,GAAUxX,EAAMyX,EAAQrQ,GAE/B,IADA,IAAIkO,EAAI,EACC/X,EAAI,EAAGiW,EAAIpM,EAAInJ,OAAQV,EAAIiW,EAAGjW,KACrC+X,EAAIlO,EAAI5J,WAAWD,IACX,IACNyC,EAAKD,SAAS0X,IAAUnC,GAEjBA,EAAI,MACXtV,EAAKD,SAAS0X,IAAU,IAAQnC,GAAK,GACrCtV,EAAKD,SAAS0X,IAAU,IAAY,GAAJnC,IAEzBA,EAAI,OAAUA,GAAK,OAC1BtV,EAAKD,SAAS0X,IAAU,IAAQnC,GAAK,IACrCtV,EAAKD,SAAS0X,IAAU,IAAQnC,GAAK,EAAK,IAC1CtV,EAAKD,SAAS0X,IAAU,IAAY,GAAJnC,KAGhC/X,IACA+X,EAAI,QAAiB,KAAJA,IAAc,GAA2B,KAApBlO,EAAI5J,WAAWD,IACrDyC,EAAKD,SAAS0X,IAAU,IAAQnC,GAAK,IACrCtV,EAAKD,SAAS0X,IAAU,IAAQnC,GAAK,GAAM,IAC3CtV,EAAKD,SAAS0X,IAAU,IAAQnC,GAAK,EAAK,IAC1CtV,EAAKD,SAAS0X,IAAU,IAAY,GAAJnC,GAGtC,CAuBA,SAASoC,GAAQ9Y,EAAO+Y,EAAQ7O,GAC9B,IAAIrN,EAAI+W,EAAU1J,GAAOvL,EAAI,EAAGiW,EAAI,EAAGoE,EAAK,EAAGC,EAAK,EAAG5Z,EAAS,EAAG0C,EAAO,EAE1E,GAAa,WAATlF,EAAmB,CAIrB,GAHAwC,EAzBJ,SAAoBmJ,GAElB,IADA,IAAIkO,EAAI,EAAGrX,EAAS,EACXV,EAAI,EAAGiW,EAAIpM,EAAInJ,OAAQV,EAAIiW,EAAGjW,KACrC+X,EAAIlO,EAAI5J,WAAWD,IACX,IACNU,GAAU,EAEHqX,EAAI,KACXrX,GAAU,EAEHqX,EAAI,OAAUA,GAAK,MAC1BrX,GAAU,GAGVV,IACAU,GAAU,GAGd,OAAOA,CACT,CAMasX,CAAWzM,GAGhB7K,EAAS,GACXW,EAAM2C,KAAc,IAATtD,GACX0C,EAAO,OAGJ,GAAI1C,EAAS,IAChBW,EAAM2C,KAAK,IAAMtD,GACjB0C,EAAO,OAGJ,GAAI1C,EAAS,MAChBW,EAAM2C,KAAK,IAAMtD,GAAU,EAAGA,GAC9B0C,EAAO,MAGJ,MAAI1C,EAAS,YAIhB,MAAM,IAAI6G,MAAM,mBAHhBlG,EAAM2C,KAAK,IAAMtD,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D0C,EAAO,CAGR,CAED,OADAgX,EAAOpW,KAAK,CAAEuW,GAAMhP,EAAOiP,GAAS9Z,EAAQ+Z,GAASpZ,EAAMX,SACpD0C,EAAO1C,CACf,CACD,GAAa,WAATxC,EAIF,OAAI2I,KAAK6T,MAAMnP,KAAWA,GAAUoP,SAASpP,GAMzCA,GAAS,EAEPA,EAAQ,KACVlK,EAAM2C,KAAKuH,GACJ,GAGLA,EAAQ,KACVlK,EAAM2C,KAAK,IAAMuH,GACV,GAGLA,EAAQ,OACVlK,EAAM2C,KAAK,IAAMuH,GAAS,EAAGA,GACtB,GAGLA,EAAQ,YACVlK,EAAM2C,KAAK,IAAMuH,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGT8O,EAAM9O,EAAQ1E,KAAKqL,IAAI,EAAG,IAAQ,EAClCoI,EAAK/O,IAAU,EACflK,EAAM2C,KAAK,IAAMqW,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,GAGH/O,IAAU,IACZlK,EAAM2C,KAAKuH,GACJ,GAGLA,IAAU,KACZlK,EAAM2C,KAAK,IAAMuH,GACV,GAGLA,IAAU,OACZlK,EAAM2C,KAAK,IAAMuH,GAAS,EAAGA,GACtB,GAGLA,IAAU,YACZlK,EAAM2C,KAAK,IAAMuH,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGT8O,EAAKxT,KAAK6T,MAAMnP,EAAQ1E,KAAKqL,IAAI,EAAG,KACpCoI,EAAK/O,IAAU,EACflK,EAAM2C,KAAK,IAAMqW,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,IAxDPjZ,EAAM2C,KAAK,KACXoW,EAAOpW,KAAK,CAAE4W,GAAQrP,EAAOiP,GAAS,EAAGC,GAASpZ,EAAMX,SACjD,GAyDX,GAAa,WAATxC,EAAmB,CAErB,GAAc,OAAVqN,EAEF,OADAlK,EAAM2C,KAAK,KACJ,EAGT,GAAIc,MAAM+V,QAAQtP,GAAQ,CAIxB,IAHA7K,EAAS6K,EAAM7K,QAGF,GACXW,EAAM2C,KAAc,IAATtD,GACX0C,EAAO,OAGJ,GAAI1C,EAAS,MAChBW,EAAM2C,KAAK,IAAMtD,GAAU,EAAGA,GAC9B0C,EAAO,MAGJ,MAAI1C,EAAS,YAIhB,MAAM,IAAI6G,MAAM,mBAHhBlG,EAAM2C,KAAK,IAAMtD,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D0C,EAAO,CAGR,CACD,IAAKpD,EAAI,EAAGA,EAAIU,EAAQV,IACtBoD,GAAQ+W,GAAQ9Y,EAAO+Y,EAAQ7O,EAAMvL,IAEvC,OAAOoD,CACR,CAGD,GAAImI,aAAiB5E,KAAM,CACzB,IAAImU,EAAOvP,EAAMwP,UAIjB,OAHAV,EAAKxT,KAAK6T,MAAMI,EAAOjU,KAAKqL,IAAI,EAAG,KACnCoI,EAAKQ,IAAS,EACdzZ,EAAM2C,KAAK,IAAM,EAAGqW,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GAC3E,EACR,CAED,GAAI/O,aAAiB7M,YAAa,CAIhC,IAHAgC,EAAS6K,EAAM1L,YAGF,IACXwB,EAAM2C,KAAK,IAAMtD,GACjB0C,EAAO,OAGT,GAAI1C,EAAS,MACXW,EAAM2C,KAAK,IAAMtD,GAAU,EAAGA,GAC9B0C,EAAO,MAGT,MAAI1C,EAAS,YAIX,MAAM,IAAI6G,MAAM,oBAHhBlG,EAAM2C,KAAK,IAAMtD,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D0C,EAAO,CAGR,CAED,OADAgX,EAAOpW,KAAK,CAAEgX,GAAMzP,EAAOiP,GAAS9Z,EAAQ+Z,GAASpZ,EAAMX,SACpD0C,EAAO1C,CACf,CAED,GAA4B,mBAAjB6K,EAAM0P,OACf,OAAOd,GAAQ9Y,EAAO+Y,EAAQ7O,EAAM0P,UAGtC,IAAIpd,EAAO,GAAIE,EAAM,GAEjBmd,EAAUxd,OAAOG,KAAK0N,GAC1B,IAAKvL,EAAI,EAAGiW,EAAIiF,EAAQxa,OAAQV,EAAIiW,EAAGjW,IAEX,mBAAfuL,EADXxN,EAAMmd,EAAQlb,KAEZnC,EAAKmG,KAAKjG,GAMd,IAHA2C,EAAS7C,EAAK6C,QAGD,GACXW,EAAM2C,KAAc,IAATtD,GACX0C,EAAO,OAGJ,GAAI1C,EAAS,MAChBW,EAAM2C,KAAK,IAAMtD,GAAU,EAAGA,GAC9B0C,EAAO,MAGJ,MAAI1C,EAAS,YAIhB,MAAM,IAAI6G,MAAM,oBAHhBlG,EAAM2C,KAAK,IAAMtD,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D0C,EAAO,CAGR,CAED,IAAKpD,EAAI,EAAGA,EAAIU,EAAQV,IAEtBoD,GAAQ+W,GAAQ9Y,EAAO+Y,EADvBrc,EAAMF,EAAKmC,IAEXoD,GAAQ+W,GAAQ9Y,EAAO+Y,EAAQ7O,EAAMxN,IAEvC,OAAOqF,CACR,CAED,GAAa,YAATlF,EAEF,OADAmD,EAAM2C,KAAKuH,EAAQ,IAAO,KACnB,EAGT,GAAa,cAATrN,EAEF,OADAmD,EAAM2C,KAAK,IAAM,EAAG,GACb,EAET,MAAM,IAAIuD,MAAM,mBAClB,CA0CA,IAAA4T,GAxCA,SAAgB5P,GACd,IAAIlK,EAAQ,GACR+Y,EAAS,GACThX,EAAO+W,GAAQ9Y,EAAO+Y,EAAQ7O,GAC9B6P,EAAM,IAAI1c,YAAY0E,GACtBX,EAAO,IAAIF,SAAS6Y,GAEpBC,EAAa,EACbC,EAAe,EACfC,GAAc,EACdnB,EAAO1Z,OAAS,IAClB6a,EAAanB,EAAO,GAAGK,IAIzB,IADA,IAAIe,EAAOC,EAAc,EAAGvB,EAAS,EAC5Bla,EAAI,EAAGiW,EAAI5U,EAAMX,OAAQV,EAAIiW,EAAGjW,IAEvC,GADAyC,EAAKD,SAAS8Y,EAAetb,EAAGqB,EAAMrB,IAClCA,EAAI,IAAMub,EAAd,CAIA,GAFAE,GADAD,EAAQpB,EAAOiB,IACKb,GACpBN,EAASoB,EAAeC,EACpBC,EAAMR,GAER,IADA,IAAIU,EAAM,IAAI/b,WAAW6b,EAAMR,IACtB1X,EAAI,EAAGA,EAAImY,EAAanY,IAC/Bb,EAAKD,SAAS0X,EAAS5W,EAAGoY,EAAIpY,SAEvBkY,EAAMjB,GACfN,GAAUxX,EAAMyX,EAAQsB,EAAMjB,SACJrR,IAAjBsS,EAAMZ,IACfnY,EAAKkZ,WAAWzB,EAAQsB,EAAMZ,IAGhCU,GAAgBG,EACZrB,IAFJiB,KAGEE,EAAanB,EAAOiB,GAAYZ,GAjBK,CAoBzC,OAAOW,CACT,EC5SA,SAASQ,GAAQ/c,GAEf,GADAiF,KAAK2W,GAAU,EACX5b,aAAkBH,YACpBoF,KAAK+X,GAAUhd,EACfiF,KAAKgY,GAAQ,IAAIvZ,SAASuB,KAAK+X,QAC1B,KAAInd,YAAYC,OAAOE,GAI5B,MAAM,IAAI0I,MAAM,oBAHhBzD,KAAK+X,GAAUhd,EAAOA,OACtBiF,KAAKgY,GAAQ,IAAIvZ,SAASuB,KAAK+X,GAAShd,EAAOe,WAAYf,EAAOgB,WAGnE,CACH,CA2CA+b,GAAQtd,UAAUyd,GAAS,SAAUrb,GAEnC,IADA,IAAI6K,EAAQ,IAAIzG,MAAMpE,GACbV,EAAI,EAAGA,EAAIU,EAAQV,IAC1BuL,EAAMvL,GAAK8D,KAAKkY,KAElB,OAAOzQ,CACT,EAEAqQ,GAAQtd,UAAU2d,GAAO,SAAUvb,GAEjC,IADA,IAAc6K,EAAQ,CAAA,EACbvL,EAAI,EAAGA,EAAIU,EAAQV,IAE1BuL,EADMzH,KAAKkY,MACElY,KAAKkY,KAEpB,OAAOzQ,CACT,EAEAqQ,GAAQtd,UAAUic,GAAO,SAAU7Z,GACjC,IAAI6K,EA3DN,SAAkB9I,EAAMyX,EAAQxZ,GAE9B,IADA,IAAIwb,EAAS,GAAIC,EAAM,EACdnc,EAAIka,EAAQkC,EAAMlC,EAASxZ,EAAQV,EAAIoc,EAAKpc,IAAK,CACxD,IAAIqc,EAAO5Z,EAAK6Z,SAAStc,GACzB,GAAY,IAAPqc,EAIL,GAAsB,MAAV,IAAPA,GAOL,GAAsB,MAAV,IAAPA,GAAL,CAQA,GAAsB,MAAV,IAAPA,GAaL,MAAM,IAAI9U,MAAM,gBAAkB8U,EAAK9d,SAAS,MAZ9C4d,GAAe,EAAPE,IAAgB,IACC,GAArB5Z,EAAK6Z,WAAWtc,KAAc,IACT,GAArByC,EAAK6Z,WAAWtc,KAAc,EACT,GAArByC,EAAK6Z,WAAWtc,KACT,OACTmc,GAAO,MACPD,GAAU1a,OAAOC,aAA4B,OAAd0a,IAAQ,IAA8B,OAAT,KAANA,KAEtDD,GAAU1a,OAAOC,aAAa0a,EAVjC,MANCD,GAAU1a,OAAOC,cACN,GAAP4a,IAAgB,IACK,GAArB5Z,EAAK6Z,WAAWtc,KAAc,EACT,GAArByC,EAAK6Z,WAAWtc,SAVpBkc,GAAU1a,OAAOC,cACN,GAAP4a,IAAgB,EACI,GAArB5Z,EAAK6Z,WAAWtc,SANnBkc,GAAU1a,OAAOC,aAAa4a,EAgCjC,CACD,OAAOH,CACT,CAoBcK,CAASzY,KAAKgY,GAAOhY,KAAK2W,GAAS/Z,GAE/C,OADAoD,KAAK2W,IAAW/Z,EACT6K,CACT,EAEAqQ,GAAQtd,UAAU0c,GAAO,SAAUta,GACjC,IAAI6K,EAAQzH,KAAK+X,GAAQtY,MAAMO,KAAK2W,GAAS3W,KAAK2W,GAAU/Z,GAE5D,OADAoD,KAAK2W,IAAW/Z,EACT6K,CACT,EAEAqQ,GAAQtd,UAAU0d,GAAS,WACzB,IACIzQ,EADAiR,EAAS1Y,KAAKgY,GAAMQ,SAASxY,KAAK2W,MAC3B/Z,EAAS,EAAGxC,EAAO,EAAGmc,EAAK,EAAGC,EAAK,EAE9C,GAAIkC,EAAS,IAEX,OAAIA,EAAS,IACJA,EAGLA,EAAS,IACJ1Y,KAAKmY,GAAc,GAATO,GAGfA,EAAS,IACJ1Y,KAAKiY,GAAgB,GAATS,GAGd1Y,KAAKyW,GAAc,GAATiC,GAInB,GAAIA,EAAS,IACX,OAA8B,GAAtB,IAAOA,EAAS,GAG1B,OAAQA,GAEN,KAAK,IACH,OAAO,KAET,KAAK,IACH,OAAO,EAET,KAAK,IACH,OAAO,EAGT,KAAK,IAGH,OAFA9b,EAASoD,KAAKgY,GAAMQ,SAASxY,KAAK2W,IAClC3W,KAAK2W,IAAW,EACT3W,KAAKkX,GAAKta,GACnB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM/J,UAAUjO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKkX,GAAKta,GACnB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKkX,GAAKta,GAGnB,KAAK,IAIH,OAHAA,EAASoD,KAAKgY,GAAMQ,SAASxY,KAAK2W,IAClCvc,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,GAAU,GACzC3W,KAAK2W,IAAW,EACT,CAACvc,EAAM4F,KAAKkX,GAAKta,IAC1B,KAAK,IAIH,OAHAA,EAASoD,KAAKgY,GAAM/J,UAAUjO,KAAK2W,IACnCvc,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,GAAU,GACzC3W,KAAK2W,IAAW,EACT,CAACvc,EAAM4F,KAAKkX,GAAKta,IAC1B,KAAK,IAIH,OAHAA,EAASoD,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IACnCvc,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,GAAU,GACzC3W,KAAK2W,IAAW,EACT,CAACvc,EAAM4F,KAAKkX,GAAKta,IAG1B,KAAK,IAGH,OAFA6K,EAAQzH,KAAKgY,GAAMY,WAAW5Y,KAAK2W,IACnC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAGH,OAFAA,EAAQzH,KAAKgY,GAAMa,WAAW7Y,KAAK2W,IACnC3W,KAAK2W,IAAW,EACTlP,EAGT,KAAK,IAGH,OAFAA,EAAQzH,KAAKgY,GAAMQ,SAASxY,KAAK2W,IACjC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAGH,OAFAA,EAAQzH,KAAKgY,GAAM/J,UAAUjO,KAAK2W,IAClC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAGH,OAFAA,EAAQzH,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IAClC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAIH,OAHA8O,EAAKvW,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IAAW5T,KAAKqL,IAAI,EAAG,IACtDoI,EAAKxW,KAAKgY,GAAM7J,UAAUnO,KAAK2W,GAAU,GACzC3W,KAAK2W,IAAW,EACTJ,EAAKC,EAGd,KAAK,IAGH,OAFA/O,EAAQzH,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,IAChC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAGH,OAFAA,EAAQzH,KAAKgY,GAAMc,SAAS9Y,KAAK2W,IACjC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAGH,OAFAA,EAAQzH,KAAKgY,GAAMe,SAAS/Y,KAAK2W,IACjC3W,KAAK2W,IAAW,EACTlP,EACT,KAAK,IAIH,OAHA8O,EAAKvW,KAAKgY,GAAMe,SAAS/Y,KAAK2W,IAAW5T,KAAKqL,IAAI,EAAG,IACrDoI,EAAKxW,KAAKgY,GAAM7J,UAAUnO,KAAK2W,GAAU,GACzC3W,KAAK2W,IAAW,EACTJ,EAAKC,EAGd,KAAK,IAGH,OAFApc,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,IAC/B3W,KAAK2W,IAAW,EACH,IAATvc,OACF4F,KAAK2W,IAAW,GAGX,CAACvc,EAAM4F,KAAKkX,GAAK,IAC1B,KAAK,IAGH,OAFA9c,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,IAC/B3W,KAAK2W,IAAW,EACT,CAACvc,EAAM4F,KAAKkX,GAAK,IAC1B,KAAK,IAGH,OAFA9c,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,IAC/B3W,KAAK2W,IAAW,EACT,CAACvc,EAAM4F,KAAKkX,GAAK,IAC1B,KAAK,IAGH,OAFA9c,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,IAC/B3W,KAAK2W,IAAW,EACH,IAATvc,GACFmc,EAAKvW,KAAKgY,GAAMe,SAAS/Y,KAAK2W,IAAW5T,KAAKqL,IAAI,EAAG,IACrDoI,EAAKxW,KAAKgY,GAAM7J,UAAUnO,KAAK2W,GAAU,GACzC3W,KAAK2W,IAAW,EACT,IAAI9T,KAAK0T,EAAKC,IAEhB,CAACpc,EAAM4F,KAAKkX,GAAK,IAC1B,KAAK,IAGH,OAFA9c,EAAO4F,KAAKgY,GAAMW,QAAQ3Y,KAAK2W,IAC/B3W,KAAK2W,IAAW,EACT,CAACvc,EAAM4F,KAAKkX,GAAK,KAG1B,KAAK,IAGH,OAFAta,EAASoD,KAAKgY,GAAMQ,SAASxY,KAAK2W,IAClC3W,KAAK2W,IAAW,EACT3W,KAAKyW,GAAK7Z,GACnB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM/J,UAAUjO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKyW,GAAK7Z,GACnB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKyW,GAAK7Z,GAGnB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM/J,UAAUjO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKiY,GAAOrb,GACrB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKiY,GAAOrb,GAGrB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM/J,UAAUjO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKmY,GAAKvb,GACnB,KAAK,IAGH,OAFAA,EAASoD,KAAKgY,GAAM7J,UAAUnO,KAAK2W,IACnC3W,KAAK2W,IAAW,EACT3W,KAAKmY,GAAKvb,GAGrB,MAAM,IAAI6G,MAAM,kBAClB,EAWA,IAAAuV,GATA,SAAgBje,GACd,IAAIke,EAAU,IAAInB,GAAQ/c,GACtB0M,EAAQwR,EAAQf,KACpB,GAAIe,EAAQtC,KAAY5b,EAAOgB,WAC7B,MAAM,IAAI0H,MAAO1I,EAAOgB,WAAakd,EAAQtC,GAAW,mBAE1D,OAAOlP,CACT,ECtRcyR,GAAA7a,OAAG8a,GACjBD,GAAA1b,OAAiB4b,oCCcjB,SAAS1Z,EAAQ5E,GACf,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIb,KAAOyF,EAAQlF,UACtBM,EAAIb,GAAOyF,EAAQlF,UAAUP,GAE/B,OAAOa,CACT,CAhBkB6E,CAAM7E,EACxB,CAXEue,EAAAC,QAAiB5Z,EAqCnBA,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,GACpCD,KAAKC,EAAW,IAAMH,GAASE,KAAKC,EAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,MAaTN,EAAQlF,UAAU4F,KAAO,SAASN,EAAOC,GACvC,SAASH,IACPI,KAAKK,IAAIP,EAAOF,GAChBG,EAAGO,MAAMN,KAAMO,UAChB,CAID,OAFAX,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,MAaTN,EAAQlF,UAAU6F,IAClBX,EAAQlF,UAAUgG,eAClBd,EAAQlF,UAAUiG,mBAClBf,EAAQlF,UAAUkG,oBAAsB,SAASZ,EAAOC,GAItD,GAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAGjC,GAAKM,UAAU3D,OAEjB,OADAoD,KAAKC,EAAa,GACXD,KAIT,IAUIW,EAVAC,EAAYZ,KAAKC,EAAW,IAAMH,GACtC,IAAKc,EAAW,OAAOZ,KAGvB,GAAI,GAAKO,UAAU3D,OAEjB,cADOoD,KAAKC,EAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAI0E,EAAUhE,OAAQV,IAEpC,IADAyE,EAAKC,EAAU1E,MACJ6D,GAAMY,EAAGZ,KAAOA,EAAI,CAC7Ba,EAAUC,OAAO3E,EAAG,GACpB,KACD,CASH,OAJyB,IAArB0E,EAAUhE,eACLoD,KAAKC,EAAW,IAAMH,GAGxBE,MAWTN,EAAQlF,UAAUsG,KAAO,SAAShB,GAChCE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAKrC,IAHA,IAAIc,EAAO,IAAIC,MAAMT,UAAU3D,OAAS,GACpCgE,EAAYZ,KAAKC,EAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIqE,UAAU3D,OAAQV,IACpC6E,EAAK7E,EAAI,GAAKqE,UAAUrE,GAG1B,GAAI0E,EAEG,CAAI1E,EAAI,EAAb,IAAK,IAAWkB,GADhBwD,EAAYA,EAAUnB,MAAM,IACI7C,OAAQV,EAAIkB,IAAOlB,EACjD0E,EAAU1E,GAAGoE,MAAMN,KAAMe,EADKnE,CAKlC,OAAOoD,MAWTN,EAAQlF,UAAU0G,UAAY,SAASpB,GAErC,OADAE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAC9BD,KAAKC,EAAW,IAAMH,IAAU,IAWzCJ,EAAQlF,UAAU2G,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAOlD,oBC7K9B2c,GAAUJ,GACVzZ,cAEYwI,GAAAsR,GAAAtR,SAAG,EAMfuR,GAAcC,GAAAF,GAAAC,WAAqB,CACrCE,QAAS,EACTC,WAAY,EACZC,MAAO,EACPC,IAAK,EACLC,cAAe,GAGbC,GACFnU,OAAOmU,WACP,SAAUvS,GACR,MACmB,iBAAVA,GACPoP,SAASpP,IACT1E,KAAK6T,MAAMnP,KAAWA,CAE5B,EAEIwS,GAAW,SAAUxS,GACvB,MAAwB,iBAAVA,CAChB,EAEIyS,GAAW,SAAUzS,GACvB,MAAiD,oBAA1C7N,OAAOY,UAAUC,SAASC,KAAK+M,EACxC,EAEA,SAAS0S,KAAY,CAMrB,SAASrC,KAAY,CAJrBqC,GAAQ3f,UAAU6D,OAAS,SAAUN,GACnC,MAAO,CAACwb,GAAQlb,OAAON,GACzB,EAIA2B,GAAQoY,GAAQtd,WAEhBsd,GAAQtd,UAAU4f,IAAM,SAAUtf,GAChC,IAAI+B,EAAU0c,GAAQ/b,OAAO1C,GAC7BkF,KAAKqa,YAAYxd,GACjBmD,KAAKc,KAAK,UAAWjE,EACvB,EAeAib,GAAQtd,UAAU6f,YAAc,SAAUxd,GAKxC,KAHEmd,GAAUnd,EAAQzC,OAClByC,EAAQzC,MAAQqf,GAAWE,SAC3B9c,EAAQzC,MAAQqf,GAAWM,eAE3B,MAAM,IAAItW,MAAM,uBAGlB,IAAKwW,GAASpd,EAAQyd,KACpB,MAAM,IAAI7W,MAAM,qBAGlB,IA1BF,SAAqB5G,GACnB,OAAQA,EAAQzC,MACd,KAAKqf,GAAWE,QACd,YAAwBvU,IAAjBvI,EAAQxC,MAAsB6f,GAASrd,EAAQxC,MACxD,KAAKof,GAAWG,WACd,YAAwBxU,IAAjBvI,EAAQxC,KACjB,KAAKof,GAAWM,cACd,OAAOE,GAASpd,EAAQxC,OAAS6f,GAASrd,EAAQxC,MACpD,QACE,OAAO2G,MAAM+V,QAAQla,EAAQxC,MAEnC,CAeOkgB,CAAY1d,GACf,MAAM,IAAI4G,MAAM,mBAIlB,UADgC2B,IAAfvI,EAAQkW,IAAoBiH,GAAUnd,EAAQkW,KAE7D,MAAM,IAAItP,MAAM,oBAEpB,EAEAqU,GAAQtd,UAAUggB,QAAU,aAE5B,IAAeC,GAAAjB,GAAAW,QAAGA,GAClBO,GAAAlB,GAAA1B,QAAkBA,wGC1FX,SAASlY,GAAG9E,EAAKyR,EAAIxM,GAExB,OADAjF,EAAI8E,GAAG2M,EAAIxM,GACJ,WACHjF,EAAIuF,IAAIkM,EAAIxM,GAEpB,CCEA,IAAM4a,GAAkB/gB,OAAOghB,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACbza,eAAgB,IA0BPqV,YAAMlS,GAIf,SAAAkS,EAAYqF,EAAIZ,EAAK9X,GAAM,IAAAc,EA2EP,OA1EhBA,EAAAK,EAAAjJ,YAAOsF,MAeFmb,WAAY,EAKjB7X,EAAK8X,WAAY,EAIjB9X,EAAK+X,cAAgB,GAIrB/X,EAAKgY,WAAa,GAOlBhY,EAAKiY,GAAS,GAKdjY,EAAKkY,GAAY,EACjBlY,EAAKmY,IAAM,EAwBXnY,EAAKoY,KAAO,GACZpY,EAAKqY,MAAQ,GACbrY,EAAK4X,GAAKA,EACV5X,EAAKgX,IAAMA,EACP9X,GAAQA,EAAKoZ,OACbtY,EAAKsY,KAAOpZ,EAAKoZ,MAErBtY,EAAKqF,EAAQyC,EAAc,CAAE,EAAE5I,GAC3Bc,EAAK4X,GAAGW,IACRvY,EAAKa,OAAOb,CACpB,CACAC,EAAAsS,EAAAlS,GAAA,IAAAM,EAAA4R,EAAArb,UAuvBC,OAtuBDyJ,EAKA6X,UAAA,WACI,IAAI9b,KAAK+b,KAAT,CAEA,IAAMb,EAAKlb,KAAKkb,GAChBlb,KAAK+b,KAAO,CACRnc,GAAGsb,EAAI,OAAQlb,KAAKgM,OAAOtJ,KAAK1C,OAChCJ,GAAGsb,EAAI,SAAUlb,KAAKgc,SAAStZ,KAAK1C,OACpCJ,GAAGsb,EAAI,QAASlb,KAAKwM,QAAQ9J,KAAK1C,OAClCJ,GAAGsb,EAAI,QAASlb,KAAKoM,QAAQ1J,KAAK1C,OANlC,CAQR,EAqBAiE,EAUA4W,QAAA,WACI,OAAI7a,KAAKmb,YAETnb,KAAK8b,YACA9b,KAAKkb,GAAkB,IACxBlb,KAAKkb,GAAG/W,OACR,SAAWnE,KAAKkb,GAAGe,IACnBjc,KAAKgM,UALEhM,IAOf,EACAiE,EAGAE,KAAA,WACI,OAAOnE,KAAK6a,SAChB,EACA5W,EAeAQ,KAAA,WAAc,IAAA,IAAA5C,EAAAtB,UAAA3D,OAANmE,EAAIC,IAAAA,MAAAa,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJhB,EAAIgB,GAAAxB,UAAAwB,GAGR,OAFAhB,EAAKmb,QAAQ,WACblc,KAAKc,KAAKR,MAAMN,KAAMe,GACff,IACX,EACAiE,EAiBAnD,KAAA,SAAKyL,GACD,IAAItD,EAAIkT,EAAIC,EACZ,GAAIzB,GAAgB1Y,eAAesK,GAC/B,MAAM,IAAI9I,MAAM,IAAM8I,EAAG9R,WAAa,8BACzC,IAAA4hB,IAAAA,EAAA9b,UAAA3D,OAJOmE,MAAIC,MAAAqb,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJvb,EAAIub,EAAA/b,GAAAA,UAAA+b,GAMZ,GADAvb,EAAKmb,QAAQ3P,GACTvM,KAAK2I,EAAM4T,UAAYvc,KAAK2b,MAAMa,YAAcxc,KAAK2b,eAErD,OADA3b,KAAKyc,GAAY1b,GACVf,KAEX,IAAMjC,EAAS,CACX3D,KAAMqf,GAAWI,MACjBxf,KAAM0G,EAEVhD,QAAiB,IAGjB,GAFAA,EAAOwW,QAAQC,UAAmC,IAAxBxU,KAAK2b,MAAMnH,SAEjC,mBAAsBzT,EAAKA,EAAKnE,OAAS,GAAI,CAC7C,IAAMmW,EAAK/S,KAAKyb,MACViB,EAAM3b,EAAK4b,MACjB3c,KAAK4c,GAAqB7J,EAAI2J,GAC9B3e,EAAOgV,GAAKA,CAChB,CACA,IAAM8J,EAAyG,QAAlFV,EAA+B,QAAzBlT,EAAKjJ,KAAKkb,GAAG4B,cAA2B,IAAP7T,OAAgB,EAASA,EAAGsJ,iBAA8B,IAAP4J,OAAgB,EAASA,EAAGtY,SAC7IkZ,EAAc/c,KAAKmb,aAAyC,QAAzBiB,EAAKpc,KAAKkb,GAAG4B,cAA2B,IAAPV,OAAgB,EAASA,EAAGhI,KAYtG,OAXsBpU,KAAK2b,MAAc,WAAKkB,IAGrCE,GACL/c,KAAKgd,wBAAwBjf,GAC7BiC,KAAKjC,OAAOA,IAGZiC,KAAKsb,WAAWpb,KAAKnC,IAEzBiC,KAAK2b,MAAQ,GACN3b,IACX,EACAiE,EAGA2Y,GAAA,SAAqB7J,EAAI2J,GAAK,IACtBzT,EADsBrF,EAAA5D,KAEpB6J,EAAwC,QAA7BZ,EAAKjJ,KAAK2b,MAAM9R,eAA4B,IAAPZ,EAAgBA,EAAKjJ,KAAK2I,EAAMsU,WACtF,QAAgB7X,IAAZyE,EAAJ,CAKA,IAAMqT,EAAQld,KAAKkb,GAAG3Z,cAAa,kBACxBqC,EAAK8X,KAAK3I,GACjB,IAAK,IAAI7W,EAAI,EAAGA,EAAI0H,EAAK0X,WAAW1e,OAAQV,IACpC0H,EAAK0X,WAAWpf,GAAG6W,KAAOA,GAC1BnP,EAAK0X,WAAWza,OAAO3E,EAAG,GAGlCwgB,EAAIhiB,KAAKkJ,EAAM,IAAIH,MAAM,2BAC5B,GAAEoG,GACG9J,EAAK,WAEP6D,EAAKsX,GAAGvY,eAAeua,GAAO,IAAA,IAAAC,EAAA5c,UAAA3D,OAFnBmE,EAAIC,IAAAA,MAAAmc,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJrc,EAAIqc,GAAA7c,UAAA6c,GAGfV,EAAIpc,MAAMsD,EAAM7C,IAEpBhB,EAAGsd,WAAY,EACfrd,KAAK0b,KAAK3I,GAAMhT,CAjBhB,MAFIC,KAAK0b,KAAK3I,GAAM2J,CAoBxB,EACAzY,EAgBAqZ,YAAA,SAAY/Q,GAAa,IAAA,IAAAhG,EAAAvG,KAAAud,EAAAhd,UAAA3D,OAANmE,MAAIC,MAAAuc,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJzc,EAAIyc,EAAAjd,GAAAA,UAAAid,GACnB,OAAO,IAAInc,SAAQ,SAACC,EAASmc,GACzB,IAAM1d,EAAK,SAAC2d,EAAMC,GACd,OAAOD,EAAOD,EAAOC,GAAQpc,EAAQqc,IAEzC5d,EAAGsd,WAAY,EACftc,EAAKb,KAAKH,GACVwG,EAAKzF,KAAIR,MAATiG,EAAUgG,CAAAA,GAAElB,OAAKtK,GACrB,GACJ,EACAkD,EAKAwY,GAAA,SAAY1b,GAAM,IACV2b,EADU9V,EAAA5G,KAEuB,mBAA1Be,EAAKA,EAAKnE,OAAS,KAC1B8f,EAAM3b,EAAK4b,OAEf,IAAM5e,EAAS,CACXgV,GAAI/S,KAAKwb,KACToC,SAAU,EACVC,SAAS,EACT9c,KAAAA,EACA4a,MAAOvQ,EAAc,CAAEoR,WAAW,GAAQxc,KAAK2b,QAEnD5a,EAAKb,MAAK,SAACyH,GACP,GAAI5J,IAAW6I,EAAK2U,GAAO,GAA3B,CAKA,GADyB,OAAR5T,EAET5J,EAAO6f,SAAWhX,EAAK+B,EAAM4T,UAC7B3V,EAAK2U,GAAOhc,QACRmd,GACAA,EAAI/U,SAMZ,GADAf,EAAK2U,GAAOhc,QACRmd,EAAK,CAAA,IAAAoB,IAAAA,EAAAvd,UAAA3D,OAhBEmhB,MAAY/c,MAAA8c,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAZD,EAAYC,EAAAzd,GAAAA,UAAAyd,GAiBnBtB,EAAGpc,WAAC,EAAA,CAAA,MAAI+K,OAAK0S,GACjB,CAGJ,OADAhgB,EAAO8f,SAAU,EACVjX,EAAKqX,IAjBZ,CAkBJ,IACAje,KAAKub,GAAOrb,KAAKnC,GACjBiC,KAAKie,IACT,EACAha,EAMAga,GAAA,WAA2B,IAAfC,EAAK3d,UAAA3D,OAAA,QAAAwI,IAAA7E,UAAA,IAAAA,UAAA,GACb,GAAKP,KAAKmb,WAAoC,IAAvBnb,KAAKub,GAAO3e,OAAnC,CAGA,IAAMmB,EAASiC,KAAKub,GAAO,GACvBxd,EAAO8f,UAAYK,IAGvBngB,EAAO8f,SAAU,EACjB9f,EAAO6f,WACP5d,KAAK2b,MAAQ5d,EAAO4d,MACpB3b,KAAKc,KAAKR,MAAMN,KAAMjC,EAAOgD,MAR7B,CASJ,EACAkD,EAMAlG,OAAA,SAAOA,GACHA,EAAOuc,IAAMta,KAAKsa,IAClBta,KAAKkb,GAAGlO,GAAQjP,EACpB,EACAkG,EAKA+H,OAAA,WAAS,IAAAnF,EAAA7G,KACmB,mBAAbA,KAAK4b,KACZ5b,KAAK4b,MAAK,SAACvhB,GACPwM,EAAKsX,GAAmB9jB,EAC5B,IAGA2F,KAAKme,GAAmBne,KAAK4b,KAErC,EACA3X,EAMAka,GAAA,SAAmB9jB,GACf2F,KAAKjC,OAAO,CACR3D,KAAMqf,GAAWE,QACjBtf,KAAM2F,KAAKoe,GACLhT,EAAc,CAAEiT,IAAKre,KAAKoe,GAAMhI,OAAQpW,KAAKse,IAAejkB,GAC5DA,GAEd,EACA4J,EAMAuI,QAAA,SAAQ7E,GACC3H,KAAKmb,WACNnb,KAAKiB,aAAa,gBAAiB0G,EAE3C,EACA1D,EAOAmI,QAAA,SAAQjJ,EAAQC,GACZpD,KAAKmb,WAAY,SACVnb,KAAK+S,GACZ/S,KAAKiB,aAAa,aAAckC,EAAQC,GACxCpD,KAAKue,IACT,EACAta,EAMAsa,GAAA,WAAa,IAAApT,EAAAnL,KACTpG,OAAOG,KAAKiG,KAAK0b,MAAM1hB,SAAQ,SAAC+Y,GAE5B,IADmB5H,EAAKmQ,WAAWkD,MAAK,SAACzgB,GAAM,OAAKL,OAAOK,EAAOgV,MAAQA,KACzD,CAEb,IAAM2J,EAAMvR,EAAKuQ,KAAK3I,UACf5H,EAAKuQ,KAAK3I,GACb2J,EAAIW,WACJX,EAAIhiB,KAAKyQ,EAAM,IAAI1H,MAAM,gCAEjC,CACJ,GACJ,EACAQ,EAMA+X,SAAA,SAASje,GAEL,GADsBA,EAAOuc,MAAQta,KAAKsa,IAG1C,OAAQvc,EAAO3D,MACX,KAAKqf,GAAWE,QACR5b,EAAO1D,MAAQ0D,EAAO1D,KAAKgN,IAC3BrH,KAAKye,UAAU1gB,EAAO1D,KAAKgN,IAAKtJ,EAAO1D,KAAKgkB,KAG5Cre,KAAKiB,aAAa,gBAAiB,IAAIwC,MAAM,8LAEjD,MACJ,KAAKgW,GAAWI,MAChB,KAAKJ,GAAWiF,aACZ1e,KAAK2e,QAAQ5gB,GACb,MACJ,KAAK0b,GAAWK,IAChB,KAAKL,GAAWmF,WACZ5e,KAAK6e,MAAM9gB,GACX,MACJ,KAAK0b,GAAWG,WACZ5Z,KAAK8e,eACL,MACJ,KAAKrF,GAAWM,cACZ/Z,KAAKwa,UACL,IAAM7S,EAAM,IAAIlE,MAAM1F,EAAO1D,KAAK0kB,SAElCpX,EAAItN,KAAO0D,EAAO1D,KAAKA,KACvB2F,KAAKiB,aAAa,gBAAiB0G,GAG/C,EACA1D,EAMA0a,QAAA,SAAQ5gB,GACJ,IAAMgD,EAAOhD,EAAO1D,MAAQ,GACxB,MAAQ0D,EAAOgV,IACfhS,EAAKb,KAAKF,KAAK0c,IAAI3e,EAAOgV,KAE1B/S,KAAKmb,UACLnb,KAAKgf,UAAUje,GAGff,KAAKqb,cAAcnb,KAAKtG,OAAOghB,OAAO7Z,KAE7CkD,EACD+a,UAAA,SAAUje,GACN,GAAIf,KAAKif,IAAiBjf,KAAKif,GAAcriB,OAAQ,CACjD,IACgCsiB,EADaC,EAAAC,EAA3Bpf,KAAKif,GAAcxf,SACL,IAAhC,IAAA0f,EAAAE,MAAAH,EAAAC,EAAAjR,KAAAc,MAAkC,CAAfkQ,EAAAzX,MACNnH,MAAMN,KAAMe,EACzB,CAAC,CAAA,MAAA4G,GAAAwX,EAAA3V,EAAA7B,EAAA,CAAA,QAAAwX,EAAAG,GAAA,CACL,CACA3b,EAAAnJ,UAAMsG,KAAKR,MAAMN,KAAMe,GACnBf,KAAKoe,IAAQrd,EAAKnE,QAA2C,iBAA1BmE,EAAKA,EAAKnE,OAAS,KACtDoD,KAAKse,GAAcvd,EAAKA,EAAKnE,OAAS,GAE9C,EACAqH,EAKAyY,IAAA,SAAI3J,GACA,IAAMtR,EAAOzB,KACTuf,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAAK,IAAA,IAAAC,EAAAjf,UAAA3D,OAJImE,EAAIC,IAAAA,MAAAwe,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ1e,EAAI0e,GAAAlf,UAAAkf,GAKpBhe,EAAK1D,OAAO,CACR3D,KAAMqf,GAAWK,IACjB/G,GAAIA,EACJ1Y,KAAM0G,GALN,EAQZ,EACAkD,EAMA4a,MAAA,SAAM9gB,GACF,IAAM2e,EAAM1c,KAAK0b,KAAK3d,EAAOgV,IACV,mBAAR2J,WAGJ1c,KAAK0b,KAAK3d,EAAOgV,IAEpB2J,EAAIW,WACJtf,EAAO1D,KAAK6hB,QAAQ,MAGxBQ,EAAIpc,MAAMN,KAAMjC,EAAO1D,MAC3B,EACA4J,EAKAwa,UAAA,SAAU1L,EAAIsL,GACVre,KAAK+S,GAAKA,EACV/S,KAAKob,UAAYiD,GAAOre,KAAKoe,KAASC,EACtCre,KAAKoe,GAAOC,EACZre,KAAKmb,WAAY,EACjBnb,KAAK0f,eACL1f,KAAKiB,aAAa,WAClBjB,KAAKie,IAAY,EACrB,EACAha,EAKAyb,aAAA,WAAe,IAAA5K,EAAA9U,KACXA,KAAKqb,cAAcrhB,SAAQ,SAAC+G,GAAI,OAAK+T,EAAKkK,UAAUje,MACpDf,KAAKqb,cAAgB,GACrBrb,KAAKsb,WAAWthB,SAAQ,SAAC+D,GACrB+W,EAAKkI,wBAAwBjf,GAC7B+W,EAAK/W,OAAOA,EAChB,IACAiC,KAAKsb,WAAa,EACtB,EACArX,EAKA6a,aAAA,WACI9e,KAAKwa,UACLxa,KAAKoM,QAAQ,uBACjB,EACAnI,EAOAuW,QAAA,WACQxa,KAAK+b,OAEL/b,KAAK+b,KAAK/hB,SAAQ,SAAC2lB,GAAU,OAAKA,OAClC3f,KAAK+b,UAAO3W,GAEhBpF,KAAKkb,GAAa,GAAElb,KACxB,EACAiE,EAgBA8W,WAAA,WAUI,OATI/a,KAAKmb,WACLnb,KAAKjC,OAAO,CAAE3D,KAAMqf,GAAWG,aAGnC5Z,KAAKwa,UACDxa,KAAKmb,WAELnb,KAAKoM,QAAQ,wBAEVpM,IACX,EACAiE,EAKAK,MAAA,WACI,OAAOtE,KAAK+a,YAChB,EACA9W,EASAuQ,SAAA,SAASA,GAEL,OADAxU,KAAK2b,MAAMnH,SAAWA,EACfxU,IACX,EAcAiE,EAaA4F,QAAA,SAAQA,GAEJ,OADA7J,KAAK2b,MAAM9R,QAAUA,EACd7J,IACX,EACAiE,EAWA2b,MAAA,SAAMlP,GAGF,OAFA1Q,KAAKif,GAAgBjf,KAAKif,IAAiB,GAC3Cjf,KAAKif,GAAc/e,KAAKwQ,GACjB1Q,IACX,EACAiE,EAWA4b,WAAA,SAAWnP,GAGP,OAFA1Q,KAAKif,GAAgBjf,KAAKif,IAAiB,GAC3Cjf,KAAKif,GAAc/C,QAAQxL,GACpB1Q,IACX,EACAiE,EAkBA6b,OAAA,SAAOpP,GACH,IAAK1Q,KAAKif,GACN,OAAOjf,KAEX,GAAI0Q,GAEA,IADA,IAAMxP,EAAYlB,KAAKif,GACd/iB,EAAI,EAAGA,EAAIgF,EAAUtE,OAAQV,IAClC,GAAIwU,IAAaxP,EAAUhF,GAEvB,OADAgF,EAAUL,OAAO3E,EAAG,GACb8D,UAKfA,KAAKif,GAAgB,GAEzB,OAAOjf,IACX,EACAiE,EAIA8b,aAAA,WACI,OAAO/f,KAAKif,IAAiB,EACjC,EACAhb,EAaA+b,cAAA,SAActP,GAGV,OAFA1Q,KAAKigB,GAAwBjgB,KAAKigB,IAAyB,GAC3DjgB,KAAKigB,GAAsB/f,KAAKwQ,GACzB1Q,IACX,EACAiE,EAaAic,mBAAA,SAAmBxP,GAGf,OAFA1Q,KAAKigB,GAAwBjgB,KAAKigB,IAAyB,GAC3DjgB,KAAKigB,GAAsB/D,QAAQxL,GAC5B1Q,IACX,EACAiE,EAkBAkc,eAAA,SAAezP,GACX,IAAK1Q,KAAKigB,GACN,OAAOjgB,KAEX,GAAI0Q,GAEA,IADA,IAAMxP,EAAYlB,KAAKigB,GACd/jB,EAAI,EAAGA,EAAIgF,EAAUtE,OAAQV,IAClC,GAAIwU,IAAaxP,EAAUhF,GAEvB,OADAgF,EAAUL,OAAO3E,EAAG,GACb8D,UAKfA,KAAKigB,GAAwB,GAEjC,OAAOjgB,IACX,EACAiE,EAIAmc,qBAAA,WACI,OAAOpgB,KAAKigB,IAAyB,EACzC,EACAhc,EAOA+Y,wBAAA,SAAwBjf,GACpB,GAAIiC,KAAKigB,IAAyBjgB,KAAKigB,GAAsBrjB,OAAQ,CACjE,IACgCyjB,EADqBC,EAAAlB,EAAnCpf,KAAKigB,GAAsBxgB,SACb,IAAhC,IAAA6gB,EAAAjB,MAAAgB,EAAAC,EAAApS,KAAAc,MAAkC,CAAfqR,EAAA5Y,MACNnH,MAAMN,KAAMjC,EAAO1D,KAChC,CAAC,CAAA,MAAAsN,GAAA2Y,EAAA9W,EAAA7B,EAAA,CAAA,QAAA2Y,EAAAhB,GAAA,CACL,GACH/X,EAAAsO,EAAA,CAAA,CAAA5b,IAAA,eAAAuN,IAzuBD,WACI,OAAQxH,KAAKmb,SACjB,GAAC,CAAAlhB,IAAA,SAAAuN,IAkCD,WACI,QAASxH,KAAK+b,IAClB,GAAC,CAAA9hB,IAAA,WAAAuN,IAsgBD,WAEI,OADAxH,KAAK2b,MAAc,UAAG,EACf3b,IACX,IAAC,EA9oBuBN,GC7BrB,SAAS6gB,GAAQ/d,GACpBA,EAAOA,GAAQ,GACfxC,KAAKwgB,GAAKhe,EAAKie,KAAO,IACtBzgB,KAAK0gB,IAAMle,EAAKke,KAAO,IACvB1gB,KAAK2gB,OAASne,EAAKme,QAAU,EAC7B3gB,KAAK4gB,OAASpe,EAAKoe,OAAS,GAAKpe,EAAKoe,QAAU,EAAIpe,EAAKoe,OAAS,EAClE5gB,KAAK6gB,SAAW,CACpB,CAOAN,GAAQ/lB,UAAUsmB,SAAW,WACzB,IAAIN,EAAKxgB,KAAKwgB,GAAKzd,KAAKqL,IAAIpO,KAAK2gB,OAAQ3gB,KAAK6gB,YAC9C,GAAI7gB,KAAK4gB,OAAQ,CACb,IAAIG,EAAOhe,KAAKC,SACZge,EAAYje,KAAK6T,MAAMmK,EAAO/gB,KAAK4gB,OAASJ,GAChDA,EAA8B,EAAxBzd,KAAK6T,MAAa,GAAPmK,GAAwCP,EAAKQ,EAAtBR,EAAKQ,CACjD,CACA,OAAgC,EAAzBje,KAAK0d,IAAID,EAAIxgB,KAAK0gB,IAC7B,EAMAH,GAAQ/lB,UAAUymB,MAAQ,WACtBjhB,KAAK6gB,SAAW,CACpB,EAMAN,GAAQ/lB,UAAU0mB,OAAS,SAAUT,GACjCzgB,KAAKwgB,GAAKC,CACd,EAMAF,GAAQ/lB,UAAU2mB,OAAS,SAAUT,GACjC1gB,KAAK0gB,IAAMA,CACf,EAMAH,GAAQ/lB,UAAU4mB,UAAY,SAAUR,GACpC5gB,KAAK4gB,OAASA,CAClB,EC3DaS,IAAAA,YAAO1d,GAChB,SAAA0d,EAAYna,EAAK1E,GAAM,IAAAc,EACf2F,GACJ3F,EAAAK,EAAAjJ,YAAOsF,MACFshB,KAAO,GACZhe,EAAKyY,KAAO,GACR7U,GAAO,WAAQiK,EAAYjK,KAC3B1E,EAAO0E,EACPA,OAAM9B,IAEV5C,EAAOA,GAAQ,IACV+C,KAAO/C,EAAK+C,MAAQ,aACzBjC,EAAKd,KAAOA,EACZD,EAAqBe,EAAOd,GAC5Bc,EAAKie,cAAmC,IAAtB/e,EAAK+e,cACvBje,EAAKke,qBAAqBhf,EAAKgf,sBAAwBtQ,KACvD5N,EAAKme,kBAAkBjf,EAAKif,mBAAqB,KACjDne,EAAKoe,qBAAqBlf,EAAKkf,sBAAwB,KACvDpe,EAAKqe,oBAAwD,QAAnC1Y,EAAKzG,EAAKmf,2BAAwC,IAAP1Y,EAAgBA,EAAK,IAC1F3F,EAAKse,QAAU,IAAIrB,GAAQ,CACvBE,IAAKnd,EAAKme,oBACVf,IAAKpd,EAAKoe,uBACVd,OAAQtd,EAAKqe,wBAEjBre,EAAKuG,QAAQ,MAAQrH,EAAKqH,QAAU,IAAQrH,EAAKqH,SACjDvG,EAAK2Y,GAAc,SACnB3Y,EAAK4D,IAAMA,EACX,IAAM2a,EAAUrf,EAAKsf,QAAUA,GAKf,OAJhBxe,EAAKye,QAAU,IAAIF,EAAQ1H,QAC3B7W,EAAK2V,QAAU,IAAI4I,EAAQ/J,QAC3BxU,EAAKuY,IAAoC,IAArBrZ,EAAKwf,YACrB1e,EAAKuY,IACLvY,EAAKa,OAAOb,CACpB,CAACC,EAAA8d,EAAA1d,GAAA,IAAAM,EAAAod,EAAA7mB,UAsUA,OAtUAyJ,EACDsd,aAAA,SAAaU,GACT,OAAK1hB,UAAU3D,QAEfoD,KAAKkiB,KAAkBD,EAClBA,IACDjiB,KAAKmiB,eAAgB,GAElBniB,MALIA,KAAKkiB,IAMnBje,EACDud,qBAAA,SAAqBS,GACjB,YAAU7c,IAAN6c,EACOjiB,KAAKoiB,IAChBpiB,KAAKoiB,GAAwBH,EACtBjiB,OACViE,EACDwd,kBAAA,SAAkBQ,GACd,IAAIhZ,EACJ,YAAU7D,IAAN6c,EACOjiB,KAAKqiB,IAChBriB,KAAKqiB,GAAqBJ,EACF,QAAvBhZ,EAAKjJ,KAAK4hB,eAA4B,IAAP3Y,GAAyBA,EAAGiY,OAAOe,GAC5DjiB,OACViE,EACD0d,oBAAA,SAAoBM,GAChB,IAAIhZ,EACJ,YAAU7D,IAAN6c,EACOjiB,KAAKsiB,IAChBtiB,KAAKsiB,GAAuBL,EACJ,QAAvBhZ,EAAKjJ,KAAK4hB,eAA4B,IAAP3Y,GAAyBA,EAAGmY,UAAUa,GAC/DjiB,OACViE,EACDyd,qBAAA,SAAqBO,GACjB,IAAIhZ,EACJ,YAAU7D,IAAN6c,EACOjiB,KAAKuiB,IAChBviB,KAAKuiB,GAAwBN,EACL,QAAvBhZ,EAAKjJ,KAAK4hB,eAA4B,IAAP3Y,GAAyBA,EAAGkY,OAAOc,GAC5DjiB,OACViE,EACD4F,QAAA,SAAQoY,GACJ,OAAK1hB,UAAU3D,QAEfoD,KAAKwiB,GAAWP,EACTjiB,MAFIA,KAAKwiB,EAGpB,EACAve,EAMAwe,qBAAA,YAESziB,KAAK0iB,IACN1iB,KAAKkiB,IACqB,IAA1BliB,KAAK4hB,QAAQf,UAEb7gB,KAAK2iB,WAEb,EACA1e,EAOAE,KAAA,SAAKpE,GAAI,IAAA6D,EAAA5D,KACL,IAAKA,KAAKic,GAAYvW,QAAQ,QAC1B,OAAO1F,KACXA,KAAK8c,OAAS,IAAI8F,GAAO5iB,KAAKkH,IAAKlH,KAAKwC,MACxC,IAAMuB,EAAS/D,KAAK8c,OACdrb,EAAOzB,KACbA,KAAKic,GAAc,UACnBjc,KAAKmiB,eAAgB,EAErB,IAAMU,EAAiBjjB,GAAGmE,EAAQ,QAAQ,WACtCtC,EAAKuK,SACLjM,GAAMA,GACV,IACMmE,EAAU,SAACyD,GACb/D,EAAKwR,UACLxR,EAAKqY,GAAc,SACnBrY,EAAK3C,aAAa,QAAS0G,GACvB5H,EACAA,EAAG4H,GAIH/D,EAAK6e,wBAIPK,EAAWljB,GAAGmE,EAAQ,QAASG,GACrC,IAAI,IAAUlE,KAAKwiB,GAAU,CACzB,IAAM3Y,EAAU7J,KAAKwiB,GAEftF,EAAQld,KAAKuB,cAAa,WAC5BshB,IACA3e,EAAQ,IAAIT,MAAM,YAClBM,EAAOO,OACV,GAAEuF,GACC7J,KAAKwC,KAAKyJ,WACViR,EAAM/Q,QAEVnM,KAAK+b,KAAK7b,MAAK,WACX0D,EAAKjB,eAAeua,EACxB,GACJ,CAGA,OAFAld,KAAK+b,KAAK7b,KAAK2iB,GACf7iB,KAAK+b,KAAK7b,KAAK4iB,GACR9iB,IACX,EACAiE,EAMA4W,QAAA,SAAQ9a,GACJ,OAAOC,KAAKmE,KAAKpE,EACrB,EACAkE,EAKA+H,OAAA,WAEIhM,KAAKoV,UAELpV,KAAKic,GAAc,OACnBjc,KAAKiB,aAAa,QAElB,IAAM8C,EAAS/D,KAAK8c,OACpB9c,KAAK+b,KAAK7b,KAAKN,GAAGmE,EAAQ,OAAQ/D,KAAK+iB,OAAOrgB,KAAK1C,OAAQJ,GAAGmE,EAAQ,OAAQ/D,KAAKgjB,OAAOtgB,KAAK1C,OAAQJ,GAAGmE,EAAQ,QAAS/D,KAAKwM,QAAQ9J,KAAK1C,OAAQJ,GAAGmE,EAAQ,QAAS/D,KAAKoM,QAAQ1J,KAAK1C,OAE3LJ,GAAGI,KAAKiZ,QAAS,UAAWjZ,KAAKijB,UAAUvgB,KAAK1C,OACpD,EACAiE,EAKA8e,OAAA,WACI/iB,KAAKiB,aAAa,OACtB,EACAgD,EAKA+e,OAAA,SAAO3oB,GACH,IACI2F,KAAKiZ,QAAQmB,IAAI/f,EACpB,CACD,MAAOmP,GACHxJ,KAAKoM,QAAQ,cAAe5C,EAChC,CACJ,EACAvF,EAKAgf,UAAA,SAAUllB,GAAQ,IAAAwI,EAAAvG,KAEdoB,GAAS,WACLmF,EAAKtF,aAAa,SAAUlD,EAChC,GAAGiC,KAAKuB,aACZ,EACA0C,EAKAuI,QAAA,SAAQ7E,GACJ3H,KAAKiB,aAAa,QAAS0G,EAC/B,EACA1D,EAMAF,OAAA,SAAOuW,EAAK9X,GACR,IAAIuB,EAAS/D,KAAKshB,KAAKhH,GAQvB,OAPKvW,EAII/D,KAAK6b,KAAiB9X,EAAOmf,QAClCnf,EAAO8W,WAJP9W,EAAS,IAAI8R,GAAO7V,KAAMsa,EAAK9X,GAC/BxC,KAAKshB,KAAKhH,GAAOvW,GAKdA,CACX,EACAE,EAMAkf,GAAA,SAASpf,GAEL,IADA,IACAqf,EAAA,EAAAC,EADazpB,OAAOG,KAAKiG,KAAKshB,MACR8B,EAAAC,EAAAzmB,OAAAwmB,IAAE,CAAnB,IAAM9I,EAAG+I,EAAAD,GAEV,GADepjB,KAAKshB,KAAKhH,GACd4I,OACP,MAER,CACAljB,KAAKsjB,IACT,EACArf,EAMA+I,GAAA,SAAQjP,GAEJ,IADA,IAAM0I,EAAiBzG,KAAK+hB,QAAQ1jB,OAAON,GAClC7B,EAAI,EAAGA,EAAIuK,EAAe7J,OAAQV,IACvC8D,KAAK8c,OAAOnY,MAAM8B,EAAevK,GAAI6B,EAAOwW,QAEpD,EACAtQ,EAKAmR,QAAA,WACIpV,KAAK+b,KAAK/hB,SAAQ,SAAC2lB,GAAU,OAAKA,OAClC3f,KAAK+b,KAAKnf,OAAS,EACnBoD,KAAKiZ,QAAQuB,SACjB,EACAvW,EAKAqf,GAAA,WACItjB,KAAKmiB,eAAgB,EACrBniB,KAAK0iB,IAAgB,EACrB1iB,KAAKoM,QAAQ,eACjB,EACAnI,EAKA8W,WAAA,WACI,OAAO/a,KAAKsjB,IAChB,EACArf,EASAmI,QAAA,SAAQjJ,EAAQC,GACZ,IAAI6F,EACJjJ,KAAKoV,UACkB,QAAtBnM,EAAKjJ,KAAK8c,cAA2B,IAAP7T,GAAyBA,EAAG3E,QAC3DtE,KAAK4hB,QAAQX,QACbjhB,KAAKic,GAAc,SACnBjc,KAAKiB,aAAa,QAASkC,EAAQC,GAC/BpD,KAAKkiB,KAAkBliB,KAAKmiB,eAC5BniB,KAAK2iB,WAEb,EACA1e,EAKA0e,UAAA,WAAY,IAAA/b,EAAA5G,KACR,GAAIA,KAAK0iB,IAAiB1iB,KAAKmiB,cAC3B,OAAOniB,KACX,IAAMyB,EAAOzB,KACb,GAAIA,KAAK4hB,QAAQf,UAAY7gB,KAAKoiB,GAC9BpiB,KAAK4hB,QAAQX,QACbjhB,KAAKiB,aAAa,oBAClBjB,KAAK0iB,IAAgB,MAEpB,CACD,IAAM7O,EAAQ7T,KAAK4hB,QAAQd,WAC3B9gB,KAAK0iB,IAAgB,EACrB,IAAMxF,EAAQld,KAAKuB,cAAa,WACxBE,EAAK0gB,gBAETvb,EAAK3F,aAAa,oBAAqBQ,EAAKmgB,QAAQf,UAEhDpf,EAAK0gB,eAET1gB,EAAK0C,MAAK,SAACwD,GACHA,GACAlG,EAAKihB,IAAgB,EACrBjhB,EAAKkhB,YACL/b,EAAK3F,aAAa,kBAAmB0G,IAGrClG,EAAK8hB,aAEb,IACH,GAAE1P,GACC7T,KAAKwC,KAAKyJ,WACViR,EAAM/Q,QAEVnM,KAAK+b,KAAK7b,MAAK,WACX0G,EAAKjE,eAAeua,EACxB,GACJ,CACJ,EACAjZ,EAKAsf,YAAA,WACI,IAAMC,EAAUxjB,KAAK4hB,QAAQf,SAC7B7gB,KAAK0iB,IAAgB,EACrB1iB,KAAK4hB,QAAQX,QACbjhB,KAAKiB,aAAa,YAAauiB,IAClCnC,CAAA,EAvWwB3hB,GCAvB+jB,GAAQ,CAAA,EACd,SAASxnB,GAAOiL,EAAK1E,GACE,WAAf2O,EAAOjK,KACP1E,EAAO0E,EACPA,OAAM9B,GAGV,IASI8V,EATEwI,ECHH,SAAaxc,GAAqB,IAAhB3B,EAAIhF,UAAA3D,OAAA,QAAAwI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,GAAIojB,EAAGpjB,UAAA3D,OAAA2D,EAAAA,kBAAA6E,EAC/BtK,EAAMoM,EAEVyc,EAAMA,GAA4B,oBAAb3b,UAA4BA,SAC7C,MAAQd,IACRA,EAAMyc,EAAIzb,SAAW,KAAOyb,EAAI7T,MAEjB,iBAAR5I,IACH,MAAQA,EAAIzK,OAAO,KAEfyK,EADA,MAAQA,EAAIzK,OAAO,GACbknB,EAAIzb,SAAWhB,EAGfyc,EAAI7T,KAAO5I,GAGpB,sBAAsB0c,KAAK1c,KAExBA,OADA,IAAuByc,EACjBA,EAAIzb,SAAW,KAAOhB,EAGtB,WAAaA,GAI3BpM,EAAMyU,GAAMrI,IAGXpM,EAAI6K,OACD,cAAcie,KAAK9oB,EAAIoN,UACvBpN,EAAI6K,KAAO,KAEN,eAAeie,KAAK9oB,EAAIoN,YAC7BpN,EAAI6K,KAAO,QAGnB7K,EAAIyK,KAAOzK,EAAIyK,MAAQ,IACvB,IACMuK,GADkC,IAA3BhV,EAAIgV,KAAKpK,QAAQ,KACV,IAAM5K,EAAIgV,KAAO,IAAMhV,EAAIgV,KAS/C,OAPAhV,EAAIiY,GAAKjY,EAAIoN,SAAW,MAAQ4H,EAAO,IAAMhV,EAAI6K,KAAOJ,EAExDzK,EAAI+oB,KACA/oB,EAAIoN,SACA,MACA4H,GACC6T,GAAOA,EAAIhe,OAAS7K,EAAI6K,KAAO,GAAK,IAAM7K,EAAI6K,MAChD7K,CACX,CD7CmBgpB,CAAI5c,GADnB1E,EAAOA,GAAQ,IACc+C,MAAQ,cAC/BsK,EAAS6T,EAAO7T,OAChBkD,EAAK2Q,EAAO3Q,GACZxN,EAAOme,EAAOne,KACdwe,EAAgBN,GAAM1Q,IAAOxN,KAAQke,GAAM1Q,GAAU,KAkB3D,OAjBsBvQ,EAAKwhB,UACvBxhB,EAAK,0BACL,IAAUA,EAAKyhB,WACfF,EAGA7I,EAAK,IAAImG,GAAQxR,EAAQrN,IAGpBihB,GAAM1Q,KACP0Q,GAAM1Q,GAAM,IAAIsO,GAAQxR,EAAQrN,IAEpC0Y,EAAKuI,GAAM1Q,IAEX2Q,EAAO5f,QAAUtB,EAAKsB,QACtBtB,EAAKsB,MAAQ4f,EAAOtT,UAEjB8K,EAAGnX,OAAO2f,EAAOne,KAAM/C,EAClC,QAGA4I,EAAcnP,GAAQ,CAClBolB,QAAAA,GACAxL,OAAAA,GACAqF,GAAIjf,GACJ4e,QAAS5e"}