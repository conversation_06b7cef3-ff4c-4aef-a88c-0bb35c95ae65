const express = require('express');
const Joi = require('joi');
const pool = require('../config/database');
const { authenticateToken, requirePermission } = require('../middleware/auth');

const router = express.Router();

// Schema de validação
const playSchema = Joi.object({
  gameId: Joi.string().uuid().required(),
  scratchPercentage: Joi.number().min(0).max(100).default(0)
});

// Iniciar jogada
router.post('/', authenticateToken, requirePermission('play'), async (req, res, next) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const { error, value } = playSchema.validate(req.body);
    if (error) throw error;

    const { gameId } = value;
    const userId = req.user.id;

    // Buscar jogo
    const gameResult = await client.query(
      'SELECT * FROM games WHERE id = $1 AND is_active = true',
      [gameId]
    );

    if (gameResult.rows.length === 0) {
      throw new Error('Jogo não encontrado');
    }

    const game = gameResult.rows[0];

    // Verificar saldo do usuário
    const userResult = await client.query(
      'SELECT balance FROM users WHERE id = $1',
      [userId]
    );

    const userBalance = parseFloat(userResult.rows[0].balance);
    const gamePrice = parseFloat(game.price);

    if (userBalance < gamePrice) {
      throw new Error('Saldo insuficiente');
    }

    // Determinar prêmio baseado em probabilidade
    const prizesResult = await client.query(
      'SELECT * FROM prizes WHERE game_id = $1 AND is_active = true ORDER BY probability DESC',
      [gameId]
    );

    const prizes = prizesResult.rows;
    const randomValue = Math.random() * 100;
    let cumulativeProbability = 0;
    let selectedPrize = null;

    for (const prize of prizes) {
      cumulativeProbability += parseFloat(prize.probability);
      if (randomValue <= cumulativeProbability) {
        selectedPrize = prize;
        break;
      }
    }

    // Se não encontrou prêmio, usar o primeiro (geralmente "tente novamente")
    if (!selectedPrize) {
      selectedPrize = prizes[0];
    }

    // Gerar símbolos revelados baseado no prêmio
    let revealedSymbols = [];
    if (selectedPrize.winning_symbols && selectedPrize.winning_symbols.length > 0) {
      revealedSymbols = [...selectedPrize.winning_symbols];
    } else {
      // Símbolos aleatórios do jogo
      const gameSymbols = game.symbols || [];
      for (let i = 0; i < 9; i++) {
        const randomSymbol = gameSymbols[Math.floor(Math.random() * gameSymbols.length)];
        revealedSymbols.push(randomSymbol);
      }
    }

    // Criar jogada
    const playResult = await client.query(`
      INSERT INTO plays (user_id, game_id, prize_id, amount_paid, amount_won, revealed_symbols, status)
      VALUES ($1, $2, $3, $4, $5, $6, 'completed')
      RETURNING *
    `, [userId, gameId, selectedPrize.id, gamePrice, selectedPrize.value, revealedSymbols]);

    const play = playResult.rows[0];

    // Atualizar saldo do usuário (debitar valor do jogo)
    await client.query(
      'UPDATE users SET balance = balance - $1 WHERE id = $2',
      [gamePrice, userId]
    );

    // Registrar movimentação financeira (débito)
    await client.query(`
      INSERT INTO financial_movements (user_id, type, amount, balance_before, balance_after, reference_id, reference_type, description)
      VALUES ($1, 'bet', $2, $3, $4, $5, 'play', $6)
    `, [userId, gamePrice, userBalance, userBalance - gamePrice, play.id, `Jogada: ${game.name}`]);

    // Se ganhou prêmio, creditar valor
    if (selectedPrize.value > 0) {
      await client.query(
        'UPDATE users SET balance = balance + $1 WHERE id = $2',
        [selectedPrize.value, userId]
      );

      // Registrar movimentação financeira (crédito)
      await client.query(`
        INSERT INTO financial_movements (user_id, type, amount, balance_before, balance_after, reference_id, reference_type, description)
        VALUES ($1, 'win', $2, $3, $4, $5, 'play', $6)
      `, [userId, selectedPrize.value, userBalance - gamePrice, userBalance - gamePrice + selectedPrize.value, play.id, `Prêmio: ${selectedPrize.name}`]);
    }

    await client.query('COMMIT');

    res.json({
      message: 'Jogada realizada com sucesso',
      play: {
        id: play.id,
        gameId: play.game_id,
        amountPaid: play.amount_paid,
        amountWon: play.amount_won,
        revealedSymbols: play.revealed_symbols,
        prize: selectedPrize,
        playedAt: play.played_at
      }
    });

  } catch (error) {
    await client.query('ROLLBACK');
    next(error);
  } finally {
    client.release();
  }
});

// Histórico de jogadas do usuário
router.get('/history', authenticateToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT 
        p.*,
        g.name as game_name,
        g.image_url as game_image,
        pr.name as prize_name
      FROM plays p
      JOIN games g ON p.game_id = g.id
      LEFT JOIN prizes pr ON p.prize_id = pr.id
      WHERE p.user_id = $1
      ORDER BY p.played_at DESC
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset]);

    const countResult = await pool.query(
      'SELECT COUNT(*) FROM plays WHERE user_id = $1',
      [userId]
    );

    res.json({
      plays: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        pages: Math.ceil(countResult.rows[0].count / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// Atualizar porcentagem de raspagem
router.patch('/:id/scratch', authenticateToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    const { scratchPercentage } = req.body;
    const userId = req.user.id;

    if (scratchPercentage < 0 || scratchPercentage > 100) {
      return res.status(400).json({ error: 'Porcentagem inválida' });
    }

    const result = await pool.query(`
      UPDATE plays 
      SET scratch_percentage = $1 
      WHERE id = $2 AND user_id = $3
      RETURNING *
    `, [scratchPercentage, id, userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Jogada não encontrada' });
    }

    res.json({
      message: 'Porcentagem atualizada',
      scratchPercentage: result.rows[0].scratch_percentage
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;