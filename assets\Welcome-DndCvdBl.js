import{r as s,j as e,J as V,Q as xe}from"./app-BQ-90ELe.js";import{b as he,q as pe,L as ue,M as K,a as ge,c as fe}from"./Layout-DNqvm3Z9.js";import{S as je}from"./ScratchCard-BZKHs9RQ.js";import{C as be,a as we}from"./chevron-right-_j1jWp6k.js";import{T as ve}from"./trophy-BbiVKMm4.js";import{X as $}from"./x-B3kOKCkQ.js";import{z as Ne,F as Y}from"./transition-CWNhv1oz.js";import{T as ye}from"./Toast-CC7tbgxL.js";import{c as Ce}from"./confetti-cmrP99Fh.js";import"./smartphone-75eaVR4n.js";import"./createLucideIcon-B4f-Ja3z.js";import"./index-BpmJviCj.js";import"./gift-CjtgVLou.js";import"./user-DCHkebcf.js";import"./mail-OGd_n8xp.js";import"./chevron-down-BMUYcpF1.js";import"./index-Csm97l8O.js";import"./arrow-left-D8fDG-vR.js";import"./circle-plus-7_WLWn7F.js";import"./eye-off-C-NzvYUP.js";import"./eye-vfU2v7Jw.js";import"./user-plus-Ck9p-Prb.js";import"./circle-alert-C4VO83Ie.js";import"./circle-check-big-CFE5DJSs.js";function ze({banners:o}){const[w,x]=s.useState(0),[v,N]=s.useState(!0);s.useEffect(()=>{if(!v||o.length<=1)return;const i=setInterval(()=>{x(d=>(d+1)%o.length)},4e3);return()=>clearInterval(i)},[v,o.length]);const h=()=>{x(i=>(i+1)%o.length)},f=()=>{x(i=>(i-1+o.length)%o.length)},k=i=>{x(i)};return e.jsxs("div",{className:"relative w-full overflow-hidden rounded-[5px] bg-[#1a2332] mt-4 mb-4",onMouseEnter:()=>N(!1),onMouseLeave:()=>N(!0),children:[e.jsx("div",{className:"relative w-full h-full overflow-hidden",children:e.jsx("div",{className:"flex w-full h-full",style:{transform:`translateX(-${w*100}%)`,transition:"transform 0.6s ease"},children:o.map(i=>e.jsx("div",{className:"flex-shrink-0 w-full relative",children:e.jsx("img",{src:i.path,alt:"Image",className:"w-full object-cover block"})},i.id))})}),o.length>1&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"absolute top-1/2 -translate-y-1/2 left-6 w-8 h-8 md:left-4 md:w-5 md:h-5 flex items-center justify-center bg-black/0   text-white rounded-full  z-10 hover:scale-110 transition",onClick:f,"aria-label":"Banner anterior",children:e.jsx(be,{size:14})}),e.jsx("button",{className:"absolute top-1/2 -translate-y-1/2 right-6 w-8 h-8 md:right-4 md:w-5 md:h-5 flex items-center justify-center bg-black/00   text-white rounded-full  z-10 hover:scale-110 transition",onClick:h,"aria-label":"PrÃ³ximo banner",children:e.jsx(we,{size:14})})]}),o.length>1&&e.jsx("div",{className:"absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-10 md:bottom-4 md:gap-2",children:o.map((i,d)=>e.jsx("button",{className:`w-8 h-1 md:w-2.5 rounded-md md:h-2.5 border-none transition backdrop-blur ${d===w?"bg-[--primary-color] scale-125 shadow-[0_0_10px_rgba(89,89,234,0.6)]":"bg-white/40 hover:bg-white/60 hover:scale-110"}`,onClick:()=>k(d),"aria-label":`Ir para banner ${d+1}`},d))})]})}function ke({winners:o=[]}){const[w,x]=s.useState(0),[v,N]=s.useState(!1),[h,f]=s.useState(null),[k,i]=s.useState(!1),{prizes:d}=V().props,H=()=>{const a=["Ana","Bruno","Carlos","Daniela","Eduardo","Fernanda","Gustavo","Helena","Igor","Julia"],l=["Silva","Santos","Oliveira","Pereira","Costa","Almeida","Lima","Barros","Gomes","Souza"],u=a[Math.floor(Math.random()*a.length)],M=l[Math.floor(Math.random()*l.length)].substring(0,2)+"*".repeat(5);return`${u} ${M}`},S=Array.isArray(d)?d.sort(()=>.5-Math.random()).slice(0,10).map((a,l)=>({id:l+1,name:H(),prize:a.name??"PrÃªmio Misterioso",amount:parseFloat(a.amount??0),image:a.image??"/assets/images/default-prize.png"})):[],p=o.length>0?o:S,E=a=>(parseFloat(a)||0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}),T=a=>{f(a),i(!0)},R=()=>{i(!1),f(null)};s.useEffect(()=>{const a=setInterval(()=>{N(!0),setTimeout(()=>{x(l=>l>=p.length-1?0:l+1),N(!1)},300)},3e3);return()=>clearInterval(a)},[p.length]);const C=(()=>{const l=[];for(let u=0;u<12;u++){const A=(w+u)%p.length;l.push(p[A])}return l})();return e.jsxs("div",{className:"w-full overflow-hidden bg-gray-900/50 border border-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-stretch gap-2 md:gap-3 p-2 md:p-3",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 py-2 md:py-3 px-3 md:px-4 bg-[--primary-color] rounded-lg flex-shrink-0 min-w-[26px] md:min-w-[200px] border border-gray-700",children:[e.jsx("div",{className:"w-2 h-2 bg-white rounded-full animate-pulse"}),e.jsx("span",{className:"text-white font-bold text-sm md:text-base uppercase tracking-wide whitespace-nowrap",children:"AO VIVO"})]}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx("div",{className:`flex gap-2 md:gap-3 transition-all duration-300 ${v?"opacity-75 transform translate-x-1":"opacity-100 transform translate-x-0"}`,children:C.map((a,l)=>e.jsxs("div",{onClick:()=>T(a),className:"flex items-center justify-center gap-2 md:gap-3 py-2 md:py-3 px-3 md:px-4 select-none group rounded-lg border border-gray-700 bg-[--bg-card] hover:bg-gray-800 transition-colors cursor-pointer flex-shrink-0 min-w-[176px] md:min-w-[200px]",children:[e.jsx("img",{src:a.image,className:"size-6 md:size-8 object-contain flex-shrink-0",alt:a.prize,onError:u=>{u.target.src="/assets/images/default-prize.png"}}),e.jsxs("div",{className:"flex flex-col text-xs sm:text-sm min-w-0 flex-1",children:[e.jsx("span",{className:"font-medium text-xs text-amber-400/75 overflow-hidden text-nowrap text-ellipsis",children:a.name}),e.jsx("h1",{className:"font-medium text-xs text-muted-foreground overflow-hidden text-nowrap text-ellipsis",children:a.prize}),e.jsxs("span",{className:"font-semibold",children:[e.jsxs("span",{className:"text-emerald-300",children:["R$"," "]}),E(a.amount).replace("R$","").trim()]})]})]},`${a.id}-${w}-${l}`))})})]}),e.jsx(Ne,{show:k,as:s.Fragment,children:e.jsxs(he,{as:"div",className:"relative z-50",onClose:R,children:[e.jsx(Y,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black/40"})}),e.jsx("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:e.jsx(Y,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(pe,{className:"w-full max-w-md bg-[--bg-card] rounded-2xl p-6 shadow-lg border border-gray-700",children:h&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ve,{className:"w-6 h-6 text-[--primary-color]"}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Ganhador!"})]}),e.jsx("button",{onClick:R,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx($,{className:"w-6 h-6"})})]}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("img",{src:h.image,alt:h.prize,className:"w-24 h-24 object-contain rounded-lg bg-gray-800 p-2",onError:a=>{a.target.src="/assets/images/default-prize.png"}})}),e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-400 mb-1",children:"Ganhador"}),e.jsx("p",{className:"text-lg font-semibold text-amber-400",children:h.name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-400 mb-1",children:"PrÃªmio"}),e.jsx("p",{className:"text-base text-white font-medium",children:h.prize})]}),e.jsxs("div",{className:"bg-[--primary-color]/10 border border-[--primary-color]/20 rounded-lg p-4",children:[e.jsx("p",{className:"text-sm text-gray-400 mb-1",children:"Valor"}),e.jsx("p",{className:"text-2xl font-bold text-[--primary-color]",children:E(h.amount)})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:R,className:"w-full bg-[--primary-color] text-white py-3 rounded-lg font-semibold hover:bg-[--primary-color]/90 transition-colors",children:"Fechar"})})]})})})})]})})]})}function Qe(){var q,Z,X;const o=V().props.auth.user,{settings:w,scratchs:x,banners:v,winners:N}=V().props,[h,f]=s.useState(!1),[k,i]=s.useState(!1),[d,H]=s.useState(!1),[y,S]=s.useState(!1),[p,E]=s.useState(!1),[T,R]=s.useState([]),[F,C]=s.useState(!1),[a,l]=s.useState((o==null?void 0:o.balance_main)??0),[u,A]=s.useState(!1),[M,I]=s.useState(!1),W=s.useRef(),[ee,te]=s.useState(null),[m,se]=s.useState(null),[ae,P]=s.useState(!1),[re,D]=s.useState([]);s.useEffect(()=>{const n=new URLSearchParams(window.location.search).get("ref");n&&localStorage.setItem("ref",n)},[]);const L=({type:t,title:n,message:c,duration:g=5e3})=>{D(b=>[...b,{id:String(Date.now()),type:t,title:n,message:c,duration:g}])},ne=t=>{D(n=>n.filter(c=>c.id!==t))};x.map(t=>t.description);const[oe,z]=s.useState(!1),[r,_]=s.useState(null),G=s.useRef(null),[U,ie]=s.useState(300);s.useEffect(()=>{const t=()=>{G.current&&ie(G.current.offsetWidth)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]);const j=t=>(parseFloat(t)||0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"});function le({raspadinhas:t}){return e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6",children:t.map(({id:n,title:c,description:g,max_prizes:b,image:O,price:B,prizes:Q})=>e.jsxs("div",{className:"rounded-lg cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",style:{padding:0,backgroundImage:"linear-gradient(to bottom right, var(--appbackground) 70%, var(--primary-color) 200%)"},children:[e.jsx("div",{className:"relative",children:e.jsx("img",{src:O,alt:c,className:"w-full object-cover h-32 md:h-40 lg:h-48",style:{borderTopRightRadius:"8px",borderTopLeftRadius:"8px"}})}),e.jsxs("div",{className:"flex flex-col px-4 md:px-6 pb-2 pt-3 space-y-2",children:[e.jsx("h1",{className:"font-semibold text-sm md:text-base lg:text-lg",children:g}),e.jsx("h2",{className:"text-xs md:text-sm text-amber-400 font-medium opacity-90 uppercase",children:c})]}),e.jsxs("div",{className:"flex items-center px-4 md:px-6 pb-4 justify-between",children:[e.jsx("button",{onClick:()=>{if(!o){window.dispatchEvent(new CustomEvent("show-toast",{detail:{type:"error",title:"Login NecessÃ¡rio",message:"VocÃª precisa fazer login para jogar!"}})),window.dispatchEvent(new CustomEvent("open-auth-modal",{detail:{tab:"login"}}));return}window.scrollTo({top:0}),_({id:n,title:c,description:g,max_prizes:b,image:O,price:B,prizes:Q}),z(!0)},className:"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm md:text-base font-medium transition-all disabled:pointer-events-none disabled:opacity-50 bg-[--primary-color] text-white shadow-lg hover:shadow-xl h-10 md:h-12 rounded-lg px-4 md:px-6 cursor-pointer hover:bg-[--primary-color]/90 transform hover:scale-105",children:e.jsxs("section",{className:"flex gap-2 justify-between items-center",children:[e.jsxs("div",{className:"flex gap-2 items-center font-semibold",children:[e.jsx("svg",{fill:"currentColor",viewBox:"0 0 256 256",width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",className:"size-5 md:size-6",children:e.jsx("path",{d:"M198.51 56.09C186.44 35.4 169.92 24 152 24h-48c-17.92 0-34.44 11.4-46.51 32.09C46.21 75.42 40 101 40 128s6.21 52.58 17.49 71.91C69.56 220.6 86.08 232 104 232h48c17.92 0 34.44-11.4 46.51-32.09C209.79 180.58 216 155 216 128s-6.21-52.58-17.49-71.91Zm1.28 63.91h-32a152.8 152.8 0 0 0-9.68-48h30.59c6.12 13.38 10.16 30 11.09 48Zm-20.6-64h-28.73a83 83 0 0 0-12-16H152c10 0 19.4 6 27.19 16ZM152 216h-13.51a83 83 0 0 0 12-16h28.73C171.4 210 162 216 152 216Zm36.7-32h-30.58a152.8 152.8 0 0 0 9.68-48h32c-.94 18-4.98 34.62-11.1 48Z"})}),e.jsx("span",{className:"font-semibold",children:"Jogar"})]}),e.jsx("div",{className:"bg-black/20 rounded-lg px-2 py-1 flex items-center gap-1 text-white text-xs md:text-sm font-medium",children:j(B)})]})}),e.jsxs("button",{onClick:()=>{if(!o){window.dispatchEvent(new CustomEvent("show-toast",{detail:{type:"error",title:"Login NecessÃ¡rio",message:"VocÃª precisa fazer login para jogar!"}})),window.dispatchEvent(new CustomEvent("open-auth-modal",{detail:{tab:"login"}}));return}window.scrollTo({top:0}),_({id:n,title:c,description:g,max_prizes:b,image:O,price:B,prizes:Q}),z(!0)},className:"mt-2 text-xs md:text-sm flex items-center gap-1.5 font-semibold cursor-pointer hover:text-emerald-400 active:text-emerald-400 active:scale-95 transition-all duration-200",children:[e.jsx("svg",{viewBox:"0 0 512 512",fill:"currentColor",width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",className:"group-hover:animate-wiggle size-3 md:size-4",children:e.jsx("path",{d:"m190.5 68.8 34.8 59.2H152c-22.1 0-40-17.9-40-40s17.9-40 40-40h2.2c14.9 0 28.8 7.9 36.3 20.8zM64 88c0 14.4 3.5 28 9.6 40H32c-17.7 0-32 14.3-32 32v64c0 17.7 14.3 32 32 32h448c17.7 0 32-14.3 32-32v-64c0-17.7-14.3-32-32-32h-41.6c6.1-12 9.6-25.6 9.6-40 0-48.6-39.4-88-88-88h-2.2c-31.9 0-61.5 16.9-77.7 44.4L256 85.5l-24.1-41C215.7 16.9 186.1 0 154.2 0H152c-48.6 0-88 39.4-88 88zm336 0c0 22.1-17.9 40-40 40h-73.3l34.8-59.2c7.6-12.9 21.4-20.8 36.3-20.8h2.2c22.1 0 40 17.9 40 40zM32 288v176c0 26.5 21.5 48 48 48h144V288zm256 224h144c26.5 0 48-21.5 48-48V288H288z"})}),e.jsx("span",{children:" VER PRÃŠMIOS "}),e.jsx("svg",{width:"1em",height:"1em",fill:"none",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"size-3",children:e.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m8 4 8 8-8 8"})})]})]})]},n))})}const ce=document.querySelector('meta[name="csrf-token"]').getAttribute("content"),J=async()=>{C(!0);try{const t=await fetch("/scratch/buy",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":ce},body:JSON.stringify({scratch_card_id:r.id})});if(!t.ok){if(t.status===422){L({type:"warning",title:"ðŸ’³ Saldo Insuficiente",message:"VocÃª nÃ£o tem saldo suficiente para comprar esta raspadinha. FaÃ§a um depÃ³sito para continuar jogando!",duration:6e3}),z(!1),P(!0),C(!1);return}L({type:"error",title:"Erro na Compra",message:"Ocorreu um erro ao processar sua compra. Tente novamente."}),C(!1);return}const n=await t.json();l(c=>c-r.price),R(n.order.result.icons),te(n.order.result.prize),se(n),z(!1),i(!0),E(n.order.is_winner),f(!0),S(!1),I(!1)}catch(t){console.error(t),L({type:"error",title:"Erro de ConexÃ£o",message:"NÃ£o foi possÃ­vel conectar ao servidor. Verifique sua conexÃ£o."})}finally{C(!1)}},de=()=>{var t;(t=W.current)==null||t.reset(),f(!1),S(!1),I(!1),E(!1),J()},me=()=>{y||(S(!0),p&&(Ce(),l(m.balance_main)))};return e.jsxs(ue,{children:[e.jsx(xe,{title:"Bem vindo"}),e.jsx(K,{maxWidth:"md",show:k,onClose:()=>i(!1),children:e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"flex items-center justify-center bg-[--bg-card] py-2 px-2",children:e.jsxs("div",{className:"relative rounded-xl w-full max-w-sm md:max-w-md p-2",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:e.jsx("h2",{style:{background:"linear-gradient(45deg, rgb(255, 215, 38) 0%, rgb(8, 255, 198) 50%, rgb(237, 221, 83) 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",fontWeight:900,fontSize:"1.4rem",textAlign:"center"},children:"RASPOU ACHOU GANHOU"})}),e.jsx("div",{children:e.jsx("p",{style:{margin:"0 0 18px",color:"rgb(204, 204, 204)",fontWeight:500,fontSize:"0.8rem",textAlign:"center",background:"var(--bg-card)",padding:"5px",borderRadius:"10px"},children:"âœ“ ACHE 3 IGUAIS | â±ï¸ GANHE NA HORA!"})}),e.jsx("div",{className:"flex justify-center",children:e.jsx(je,{ref:W,width:U,height:U,finishPercent:60,brushSize:30,image:"/assets/images/Inserir um tÃ­tulo (1).png",fadeOutOnComplete:!0,onComplete:()=>{I(!0),me()},children:e.jsx("div",{className:"w-full h-full grid grid-cols-3 grid-rows-3 gap-2 p-3",children:T.map((t,n)=>{var g,b;const c=((g=m==null?void 0:m.order)==null?void 0:g.is_winner)&&M&&(t.name&&t.name===ee||!t.name&&t.amount==((b=m==null?void 0:m.order)==null?void 0:b.prize_amount));return e.jsxs("div",{className:`
          bg-gray-700 text-white flex flex-col items-center justify-center rounded-lg font-bold text-xs p-2
          ${c?"ring-2 ring-yellow-400 shadow-lg shadow-yellow-400/50":""}
        `,children:[e.jsx("img",{src:t.image,alt:t.name,className:"w-12 h-12 object-contain mb-1"}),e.jsx("span",{children:t.type=="physical"?t.name:j(t.amount)})]},n)})})})}),!p&&y&&e.jsxs("div",{className:"w-full p-5 rounded-lg text-white text-center mb-4 shadow-md mt-4 bg-gradient-to-r from-red-700 to-red-600 border border-red-500 transition-all duration-300 ease-in-out",children:[e.jsxs("div",{className:"flex items-center justify-center mb-3",children:[e.jsx($,{className:"w-7 h-7 text-yellow-400 mr-2",strokeWidth:2.5}),e.jsx("h2",{className:"text-xl sm:text-2xl font-extrabold text-white uppercase tracking-wide",children:"NÃ£o foi dessa vez"})]}),e.jsx("p",{className:"text-base sm:text-lg text-yellow-300 font-semibold px-2",children:"Mas nÃ£o desanime! A sorte pode estar na prÃ³xima raspadinha. ðŸ€"})]}),p&&y&&e.jsxs("div",{className:"w-full p-4 rounded-lg text-white text-center mb-2 shadow-lg mt-3 bg-gradient-to-r from-green-600 to-green-600",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx("svg",{className:"w-8 h-8 text-yellow-300 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("h2",{className:"text-2xl font-bold text-white uppercase tracking-wide",children:"ParabÃ©ns!"})]}),e.jsxs("p",{className:"text-xl text-yellow-300 font-bold mb-2",children:["VocÃª ganhou"," ",((Z=(q=m==null?void 0:m.order)==null?void 0:q.result)==null?void 0:Z.prize)||0,"!!!"]}),e.jsx("p",{className:"text-white text-sm",children:"O valor serÃ¡ adicionado automaticamente Ã  sua conta"})]}),e.jsxs("div",{className:"flex justify-between items-center bg-zinc-900 p-3 rounded-lg shadow-inner",children:[M&&y?e.jsxs("button",{onClick:de,type:"button",className:"flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-bold text-sm px-4 py-2 rounded-md transition",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"}),e.jsx("path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"})]}),"JOGAR NOVAMENTE"]}):e.jsx("button",{onClick:()=>{var t;(t=W.current)==null||t.autoScratch()},disabled:y,className:"flex items-center gap-2 bg-blue-600 hover:bg-green-700 text-white font-bold text-sm px-4 py-2 rounded-md transition",children:"REVELAR TUDO"}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-xs text-zinc-400",children:"SEU SALDO"}),e.jsx("p",{className:"text-white font-bold text-sm",children:j(a)})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>i(!1),className:"text-sm px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-md transition",children:"Fechar"})})]})})})}),e.jsx(K,{maxWidth:"sm",show:oe,onClose:()=>z(!1),children:e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"flex items-center justify-center bg-[--bg-card] py-2 px-2",children:e.jsxs("div",{className:"relative rounded-xl shadow-lg w-full max-w-sm p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-gray-300 text-sm font-semibold",children:r==null?void 0:r.description}),e.jsx("button",{onClick:()=>{z(!1)},className:" text-zinc-400 hover:text-zinc-200 transition-all","aria-label":"Fechar modal",children:e.jsx($,{size:20})})]}),e.jsx("img",{src:r==null?void 0:r.image,alt:"Raspa da EsperanÃ§a",className:"rounded-md object-fill w-full max-h-[165.88px]"}),e.jsx("p",{className:"text-sm text-gray-400 mt-4",children:r==null?void 0:r.title}),e.jsxs("div",{className:"border bg-[--appbackground] rounded-md p-4 mt-4",children:[e.jsxs("div",{className:"flex text-sm justify-between mb-2",children:[e.jsx("p",{className:"font-medium",children:"PreÃ§o:"}),e.jsx("span",{className:"bg-blue-600 text-white px-3 py-1 rounded-full text-sm",children:j(r==null?void 0:r.price)})]}),e.jsx("div",{className:"border-t my-2"}),e.jsxs("div",{className:"flex text-sm justify-between",children:[e.jsx("p",{className:"font-medium",children:"PrÃªmio MÃ¡ximo:"}),e.jsx("span",{className:"bg-green-600 text-white px-3 py-1 rounded-full text-sm",children:j(r==null?void 0:r.max_prizes)})]})]}),((X=r==null?void 0:r.prizes)==null?void 0:X.length)>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm font-semibold text-white mb-2",children:"Tabela de PrÃªmios:"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx("div",{className:"flex gap-3",children:[...r.prizes].sort((t,n)=>parseFloat(n.amount)-parseFloat(t.amount)).map((t,n)=>e.jsxs("div",{className:"min-w-[140px] text-center border border-zinc-700 rounded-md p-3 bg-zinc-800",children:[e.jsx("img",{src:t.image,alt:`PrÃªmio ${n+1}`,className:"mx-auto w-10 h-10 object-contain"}),e.jsx("p",{className:"mt-2 text-sm font-semibold text-white",children:t.type=="cash"?j(t.amount):e.jsx(e.Fragment,{children:t.name})})]},n))})})]}),e.jsx("button",{disabled:F,onClick:()=>J(),className:"mt-6 disabled:opacity-45 w-full h-10 flex items-center justify-center bg-[--primary-color] hover:bg-[--primary-dark] text-white font-semibold py-3 rounded-md",type:"button",children:F?e.jsx(e.Fragment,{children:e.jsx("div",{className:"flex justify-center items-center",children:e.jsx(ge,{className:"w-5 h-5 mr-2 animate-spin"})})}):e.jsxs(e.Fragment,{children:["Jogar por"," ",j(r==null?void 0:r.price)]})})]})})})}),e.jsx("div",{className:"container md:container-none mx-auto px-4 md:px-8 lg:px-12 xl:px-16 md:max-w-none",children:e.jsx("section",{className:"mb-4 md:mb-6",children:e.jsx(ze,{banners:v})})}),e.jsx("section",{className:"mb-4 md:mb-8 px-4 md:px-8 lg:px-12 xl:px-16",children:e.jsx(ke,{})}),e.jsxs("div",{className:"container md:container-none mx-auto px-4 md:px-8 lg:px-12 xl:px-16 md:max-w-none",children:[e.jsxs("section",{className:"flex justify-between items-center pb-4 md:pb-6",children:[e.jsxs("h1",{className:"flex items-center gap-2 text-xl md:text-2xl lg:text-3xl font-medium",children:[e.jsx("svg",{width:"1em",height:"1em",fill:"currentColor",className:"bi bi-fire text-amber-400 animate-pulse duration-700 text-2xl md:text-3xl",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 16c3.314 0 6-2 6-5.5 0-1.5-.5-4-2.5-6 .25 1.5-1.25 2-1.25 2C11 4 9 .5 6 0c.357 2 .5 4-2 6-1.25 1-2 2.729-2 4.5C2 14 4.686 16 8 16m0-1c-1.657 0-3-1-3-2.75 0-.75.25-2 1.25-3C6.125 10 7 10.5 7 10.5c-.375-1.25.5-3.25 2-3.5-.179 1-.25 2 1 3 .625.5 1 1.364 1 2.25C11 14 9.657 15 8 15"})}),"Destaques"," "]}),e.jsxs("a",{href:"/scratchs",className:"flex font-medium items-center gap-2 cursor-pointer hover:text-primary transition-colors text-sm md:text-base",children:[e.jsx("span",{children:"Ver mais"})," ",e.jsx("svg",{width:"1em",height:"1em",fill:"none",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"size-4 md:size-5",children:e.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m8 4 8 8-8 8"})})]})]}),e.jsx("section",{className:"mb-8 md:mb-12",children:e.jsx(le,{raspadinhas:x})})]}),e.jsx(fe,{open:ae,setOpen:P,onDepositSuccess:()=>{L({type:"success",title:"DepÃ³sito Confirmado!",message:"Seu saldo foi atualizado. Agora vocÃª pode continuar jogando!"}),P(!1),window.location.reload()}}),e.jsx(ye,{toasts:re,onRemove:ne})]})}export{Qe as default};