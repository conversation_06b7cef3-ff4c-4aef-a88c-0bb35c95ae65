# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=raspa_green
DB_USER=postgres
DB_PASSWORD=password

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Server
PORT=3000
NODE_ENV=development

# PIX Gateway
PIX_GATEWAY_URL=https://api.pixgateway.com
PIX_API_KEY=your-pix-api-key
PIX_WEBHOOK_SECRET=your-webhook-secret

# Upload
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Redis (opcional)
REDIS_URL=redis://localhost:6379

# Email (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password